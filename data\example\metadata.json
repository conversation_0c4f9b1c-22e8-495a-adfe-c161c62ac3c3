{"last_updated": "2025-05-27T20:17:43.002397", "datasets": {"weather": {"file_path": "data/example/weather_data.csv", "format": null, "shape": [721, 7], "columns": ["datetime", "ghi", "dni", "dhi", "temp_air", "wind_speed", "cloud_cover"], "loaded_at": "2025-05-27T20:17:41.913636"}, "pv_system": {"file_path": "data/example/pv_system_data.csv", "format": null, "shape": [721, 3], "columns": ["datetime", "dc_power", "ac_power"], "loaded_at": "2025-05-27T20:17:41.947354"}}, "data_quality": {"3895895009584953232": {"columns": ["datetime", "ghi", "dni", "dhi", "temp_air", "wind_speed", "cloud_cover"], "quality_report": {"row_count": 721, "column_count": 6, "missing_values": {"ghi": 0, "dni": 0, "dhi": 0, "temp_air": 0, "wind_speed": 0, "cloud_cover": 0}, "completeness": {"ghi": 100.0, "dni": 100.0, "dhi": 100.0, "temp_air": 100.0, "wind_speed": 100.0, "cloud_cover": 100.0}, "duplicates": 0, "dtypes": {"ghi": "float64", "dni": "float64", "dhi": "float64", "temp_air": "float64", "wind_speed": "float64", "cloud_cover": "float64"}, "numeric_stats": {"ghi": {"mean": 294.737931710771, "std": 334.9703054631775, "min": 0.0, "max": 912.641795837132, "median": 101.40464394553264, "skewness": 0.5744964301838592, "kurtosis": -1.3430743922174062, "outliers": 0, "outliers_percentage": 0.0}, "dni": {"mean": 341.8354276830249, "std": 337.66616957822663, "min": 0.0, "max": 854.2652674968023, "median": 283.77529481513864, "skewness": 0.14381563100052602, "kurtosis": -1.7930598932750557, "outliers": 0, "outliers_percentage": 0.0}, "dhi": {"mean": 58.00498819587139, "std": 57.98015472538333, "min": 0.0, "max": 146.87140721104362, "median": 46.7517900968363, "skewness": 0.20839962484575192, "kurtosis": -1.7159026704738343, "outliers": 0, "outliers_percentage": 0.0}, "temp_air": {"mean": 11.771068754186949, "std": 7.358440103727885, "min": -4.002048662637165, "max": 25.95482141563029, "median": 11.567947768728557, "skewness": 0.009381080751446192, "kurtosis": -1.2765454339428195, "outliers": 0, "outliers_percentage": 0.0}, "wind_speed": {"mean": 4.007658486804844, "std": 0.5747215111119963, "min": 3.000448813979265, "max": 4.998400050229988, "median": 4.022248781703867, "skewness": -0.018436277632809205, "kurtosis": -1.169803428645266, "outliers": 0, "outliers_percentage": 0.0}, "cloud_cover": {"mean": 0.2908616430336544, "std": 0.16079422508593097, "min": 0.004097239593098, "max": 0.7727681702968896, "median": 0.2710438072329317, "skewness": 0.5358702165542605, "kurtosis": -0.2513131104351145, "outliers": 9, "outliers_percentage": 1.25}}, "value_ranges": {"ghi": {"unique_count": 431, "unique_percentage": 59.78}, "dni": {"unique_count": 431, "unique_percentage": 59.78}, "dhi": {"unique_count": 431, "unique_percentage": 59.78}, "temp_air": {"unique_count": 721, "unique_percentage": 100.0}, "wind_speed": {"unique_count": 721, "unique_percentage": 100.0}, "cloud_cover": {"unique_count": 721, "unique_percentage": 100.0}}}, "preprocessing_params": {"handle_missing": "interpolate", "detect_outliers": true, "outlier_method": "zscore", "outlier_threshold": 3.0}, "preprocessing_info": {"original_shape": [721, 7], "missing_values": {"datetime": 0, "ghi": 0, "dni": 0, "dhi": 0, "temp_air": 0, "wind_speed": 0, "cloud_cover": 0}, "outliers": {"ghi": 0, "dni": 0, "dhi": 0, "temp_air": 0, "wind_speed": 0, "cloud_cover": 0}, "time_alignment": {"original_points": 721, "aligned_points": 721, "frequency": {"_seconds": 3600, "_microseconds": 0}}, "missing_values_handled": 0, "final_shape": [721, 6]}, "processed_at": "2025-05-27T20:17:41.945021"}, "-6485316987263882235": {"columns": ["datetime", "dc_power", "ac_power"], "quality_report": {"row_count": 721, "column_count": 2, "missing_values": {"dc_power": 0, "ac_power": 0}, "completeness": {"dc_power": 100.0, "ac_power": 100.0}, "duplicates": 288, "dtypes": {"dc_power": "float64", "ac_power": "float64"}, "numeric_stats": {"dc_power": {"mean": 4.521271890666865, "std": 5.11070398277217, "min": 0.0, "max": 13.847800894717697, "median": 1.5927239275932688, "skewness": 0.5581216301819404, "kurtosis": -1.3671830548416588, "outliers": 0, "outliers_percentage": 0.0}, "ac_power": {"mean": 4.307847836463989, "std": 4.879436244626075, "min": 0.0, "max": 18.31956354311885, "median": 1.4609480492391569, "skewness": 0.5785615901547506, "kurtosis": -1.2924545031282395, "outliers": 0, "outliers_percentage": 0.0}}, "value_ranges": {"dc_power": {"unique_count": 431, "unique_percentage": 59.78}, "ac_power": {"unique_count": 433, "unique_percentage": 60.06}}}, "preprocessing_params": {"handle_missing": "interpolate", "detect_outliers": true, "outlier_method": "zscore", "outlier_threshold": 3.0}, "preprocessing_info": {"original_shape": [721, 3], "missing_values": {"datetime": 0, "dc_power": 0, "ac_power": 36}, "outliers": {"dc_power": 0, "ac_power": 6}, "time_alignment": {"original_points": 721, "aligned_points": 721, "frequency": {"_seconds": 3600, "_microseconds": 0}}, "missing_values_handled": 36, "final_shape": [721, 2]}, "processed_at": "2025-05-27T20:17:41.969298"}}}