#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
历史数据模块使用示例

本示例展示如何使用HistoricalDataModule类进行数据加载、预处理、特征提取和数据分割。
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

# 导入历史数据模块
from src.data_layer.historical_data_module import HistoricalDataModule
from src.utils.data_utils import generate_synthetic_weather_data


def main():
    """主函数"""
    print("=== 历史数据模块使用示例 ===")

    # 创建示例数据目录
    os.makedirs("data/example", exist_ok=True)

    # 生成示例数据
    print("\n1. 生成示例数据...")
    generate_example_data()

    # 创建历史数据模块实例
    print("\n2. 创建历史数据模块实例...")
    hdm = HistoricalDataModule()

    # 加载数据集
    print("\n3. 加载数据集...")
    weather_df = hdm.load_dataset(
        name="weather", file_path="data/example/weather_data.csv", time_col="datetime"
    )
    print(f"天气数据集形状: {weather_df.shape}")
    print(f"天气数据集列: {weather_df.columns.tolist()}")

    pv_df = hdm.load_dataset(
        name="pv_system",
        file_path="data/example/pv_system_data.csv",
        time_col="datetime",
    )
    print(f"光伏系统数据集形状: {pv_df.shape}")
    print(f"光伏系统数据集列: {pv_df.columns.tolist()}")

    # 合并数据集
    print("\n4. 合并数据集...")
    datasets = {"weather": weather_df, "pv_system": pv_df}
    merged_df = hdm._merge_datasets(datasets, merge_on="datetime")
    print(f"合并后数据集形状: {merged_df.shape}")
    print(f"合并后数据集列: {merged_df.columns.tolist()}")
    hdm.data["merged"] = merged_df

    # 提取特征
    print("\n5. 提取特征...")
    features_df = hdm.extract_features_from_dataset(
        df=merged_df,
        dataset_name="merged",
        window_size=12,
        features=["mean", "std", "min", "max", "trend"],
    )
    print(f"提取特征后数据集形状: {features_df.shape}")
    print(f"特征列示例: {features_df.columns[:5].tolist()}")

    # 标准化数据
    print("\n6. 标准化数据...")
    standardized_df = hdm.standardize_dataset(
        df=features_df, dataset_name="features", method="minmax", feature_range=(0, 1)
    )
    print(f"标准化后数据集形状: {standardized_df.shape}")

    # 数据分割
    print("\n7. 数据分割...")
    target_col = "ac_power"
    split_result = hdm.split_dataset(
        df=standardized_df,
        dataset_name="standardized",
        target_col=target_col,
        test_size=0.2,
        val_size=0.1,
    )

    # 打印分割结果
    if "X_train" in split_result["data"]:
        # 有目标列的情况
        print(f"训练集形状: {split_result['data']['X_train'].shape}")
        print(f"验证集形状: {split_result['data']['X_val'].shape}")
        print(f"测试集形状: {split_result['data']['X_test'].shape}")
    else:
        # 无目标列的情况
        print(f"训练集形状: {split_result['data']['train'].shape}")
        print(f"验证集形状: {split_result['data']['val'].shape}")
        print(f"测试集形状: {split_result['data']['test'].shape}")

    # 保存数据集
    print("\n8. 保存数据集...")
    hdm.save_dataset(
        name="features",
        file_path="data/example/standardized_data.csv",
        format="csv",
    )

    # 保存元数据
    print("\n9. 保存元数据...")
    hdm.save_metadata(file_path="data/example/metadata.json")

    print("\n=== 示例完成 ===")


def generate_example_data():
    """生成示例数据"""
    # 生成天气数据
    start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
    end_date = datetime.now().strftime("%Y-%m-%d")

    weather_data = generate_synthetic_weather_data(
        start_date=start_date,
        end_date=end_date,
        latitude=39.9,
        longitude=116.4,
        freq="1h",
    )

    # 生成光伏系统数据
    pv_system_data = pd.DataFrame(index=weather_data.index)
    pv_system_data["datetime"] = weather_data["datetime"]

    # 模拟直流功率: 辐照度 * 效率因子
    efficiency = 0.15
    area = 100  # 平方米
    pv_system_data["dc_power"] = weather_data["ghi"] * efficiency * area / 1000  # kW

    # 模拟交流功率: 直流功率 * 逆变器效率
    inverter_efficiency = 0.95
    pv_system_data["ac_power"] = pv_system_data["dc_power"] * inverter_efficiency

    # 添加温度效应
    temp_coeff = -0.004  # 每升高1度降低0.4%
    pv_system_data["dc_power"] *= 1 + temp_coeff * (weather_data["temp_air"] - 25)
    pv_system_data["ac_power"] *= 1 + temp_coeff * (weather_data["temp_air"] - 25)

    # 确保功率不为负
    pv_system_data["dc_power"] = pv_system_data["dc_power"].clip(0)
    pv_system_data["ac_power"] = pv_system_data["ac_power"].clip(0)

    # 添加一些噪声
    noise = np.random.normal(0, 0.05, size=len(pv_system_data))
    pv_system_data["ac_power"] *= 1 + noise

    # 添加一些缺失值和异常值
    # 随机选择5%的行设置为缺失值
    missing_indices = np.random.choice(
        pv_system_data.index, size=int(len(pv_system_data) * 0.05), replace=False
    )
    pv_system_data.loc[missing_indices, "ac_power"] = np.nan

    # 随机选择2%的行设置为异常值
    anomaly_indices = np.random.choice(
        pv_system_data.index, size=int(len(pv_system_data) * 0.02), replace=False
    )
    pv_system_data.loc[anomaly_indices, "ac_power"] *= 2.5  # 异常值为正常值的2.5倍

    # 保存数据
    weather_data.to_csv("data/example/weather_data.csv", index=False)
    pv_system_data.to_csv("data/example/pv_system_data.csv", index=False)

    print(f"生成了天气数据: {len(weather_data)}行")
    print(f"生成了光伏系统数据: {len(pv_system_data)}行")


if __name__ == "__main__":
    main()
