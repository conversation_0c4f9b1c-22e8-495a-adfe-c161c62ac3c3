from django.urls import path
from . import views

app_name = "api"

urlpatterns = [
    # 原有API端点
    path("simulation-data/", views.simulation_data, name="simulation_data"),
    path("daily-energy/", views.daily_energy, name="daily_energy"),
    path("anomaly-data/", views.anomaly_data, name="anomaly_data"),
    path("system-info/", views.system_info, name="system_info"),
    path("simulation-logs/", views.simulation_logs, name="simulation_logs"),
    path("apply-settings/", views.apply_settings, name="apply_settings"),
    path("reset-simulation/", views.reset_simulation, name="reset_simulation"),
    path("pause-simulation/", views.pause_simulation, name="pause_simulation"),
    path("resume-simulation/", views.resume_simulation, name="resume_simulation"),
    path("simulation-status/", views.simulation_status, name="simulation_status"),
    path("capacity-options/", views.capacity_options, name="capacity_options"),

    # 阶段四新增API端点
    path("pause-simulation-advanced/", views.pause_simulation_advanced, name="pause_simulation_advanced"),
    path("resume-simulation-advanced/", views.resume_simulation_advanced, name="resume_simulation_advanced"),
    path("update-simulation-parameters/", views.update_simulation_parameters, name="update_simulation_parameters"),
    path("change-time-scale/", views.change_time_scale, name="change_time_scale"),
    path("advanced-simulation-status/", views.advanced_simulation_status, name="advanced_simulation_status"),
    path("query-historical-data/", views.query_historical_data, name="query_historical_data"),
    path("cleanup-old-data/", views.cleanup_old_data, name="cleanup_old_data"),
]
