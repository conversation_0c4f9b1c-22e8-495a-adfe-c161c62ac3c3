from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
import json
from dashboard.pv_model_adapter import PVModelAdapter


def simulation_data(request):
    """提供仿真数据的API端点"""
    pv_model = PVModelAdapter.get_instance()
    data = pv_model.get_simulation_data()
    return JsonResponse(data)


def daily_energy(request):
    """提供每日能量数据的API端点"""
    pv_model = PVModelAdapter.get_instance()
    data = pv_model.get_daily_energy()
    return JsonResponse(data, safe=False)


def anomaly_data(request):
    """提供异常检测数据的API端点"""
    pv_model = PVModelAdapter.get_instance()
    data = pv_model.get_detected_anomalies()
    return JsonResponse(data, safe=False)


def system_info(request):
    """提供系统信息的API端点"""
    pv_model = PVModelAdapter.get_instance()
    data = pv_model.get_system_info()
    return JsonResponse(data)


def simulation_logs(request):
    """提供仿真日志的API端点"""
    pv_model = PVModelAdapter.get_instance()
    logs = pv_model.get_simulation_logs()
    return JsonResponse({"logs": logs}, safe=False)


@csrf_exempt
def apply_settings(request):
    """应用系统设置的API端点"""
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            pv_model = PVModelAdapter.get_instance()
            success, message = pv_model.apply_settings(data)
            return JsonResponse(
                {
                    "success": success,
                    "message": message,
                }
            )
        except json.JSONDecodeError:
            return JsonResponse(
                {
                    "success": False,
                    "message": "无效的JSON数据",
                },
                status=400,
            )
        except Exception as e:
            return JsonResponse(
                {
                    "success": False,
                    "message": f"发生错误: {str(e)}",
                },
                status=500,
            )
    else:
        return JsonResponse(
            {
                "success": False,
                "message": "仅支持POST请求",
            },
            status=405,
        )


@csrf_exempt
def reset_simulation(request):
    """重置仿真数据并重新运行仿真的API端点"""
    if request.method == "POST":
        try:
            pv_model = PVModelAdapter.get_instance()
            result = pv_model.reset_simulation()
            return JsonResponse(result)
        except Exception as e:
            return JsonResponse(
                {
                    "status": "error",
                    "message": f"重置仿真时发生错误: {str(e)}",
                },
                status=500,
            )
    else:
        return JsonResponse(
            {
                "status": "error",
                "message": "仅支持POST请求",
            },
            status=405,
        )


@csrf_exempt
def pause_simulation(request):
    """暂停仿真的API端点"""
    if request.method == "POST":
        try:
            pv_model = PVModelAdapter.get_instance()
            result = pv_model.pause_simulation()
            return JsonResponse(result)
        except Exception as e:
            return JsonResponse(
                {
                    "status": "error",
                    "message": f"暂停仿真时发生错误: {str(e)}",
                },
                status=500,
            )
    else:
        return JsonResponse(
            {
                "status": "error",
                "message": "仅支持POST请求",
            },
            status=405,
        )


@csrf_exempt
def resume_simulation(request):
    """恢复仿真的API端点"""
    if request.method == "POST":
        try:
            pv_model = PVModelAdapter.get_instance()
            result = pv_model.resume_simulation()
            return JsonResponse(result)
        except Exception as e:
            return JsonResponse(
                {
                    "status": "error",
                    "message": f"恢复仿真时发生错误: {str(e)}",
                },
                status=500,
            )
    else:
        return JsonResponse(
            {
                "status": "error",
                "message": "仅支持POST请求",
            },
            status=405,
        )


def simulation_status(request):
    """获取仿真状态的API端点"""
    pv_model = PVModelAdapter.get_instance()
    status = pv_model.get_simulation_status()
    return JsonResponse(status)


def capacity_options(request):
    """获取系统可用容量选项的API端点"""
    pv_model = PVModelAdapter.get_instance()
    options = pv_model.get_available_capacity_options()
    return JsonResponse({"options": options}, safe=False)


# ==================== 阶段四新增API端点 ====================

@csrf_exempt
def pause_simulation_advanced(request):
    """高级仿真暂停API端点"""
    if request.method != 'POST':
        return JsonResponse({"success": False, "message": "仅支持POST请求"})

    try:
        pv_model = PVModelAdapter.get_instance()
        if hasattr(pv_model, 'pause_simulation_advanced'):
            result = pv_model.pause_simulation_advanced()
        else:
            # 回退到基础暂停功能
            success, message = pv_model.pause_simulation()
            result = {"success": success, "message": message, "method": "basic"}

        return JsonResponse(result)
    except Exception as e:
        return JsonResponse({"success": False, "message": f"暂停仿真失败: {e}"})


@csrf_exempt
def resume_simulation_advanced(request):
    """高级仿真恢复API端点"""
    if request.method != 'POST':
        return JsonResponse({"success": False, "message": "仅支持POST请求"})

    try:
        pv_model = PVModelAdapter.get_instance()
        if hasattr(pv_model, 'resume_simulation_advanced'):
            result = pv_model.resume_simulation_advanced()
        else:
            # 回退到基础恢复功能
            success, message = pv_model.resume_simulation()
            result = {"success": success, "message": message, "method": "basic"}

        return JsonResponse(result)
    except Exception as e:
        return JsonResponse({"success": False, "message": f"恢复仿真失败: {e}"})


@csrf_exempt
def update_simulation_parameters(request):
    """动态更新仿真参数API端点"""
    if request.method != 'POST':
        return JsonResponse({"success": False, "message": "仅支持POST请求"})

    try:
        data = json.loads(request.body)
        params = data.get('parameters', {})

        if not params:
            return JsonResponse({"success": False, "message": "未提供参数"})

        pv_model = PVModelAdapter.get_instance()
        if hasattr(pv_model, 'update_simulation_parameters'):
            result = pv_model.update_simulation_parameters(params)
        else:
            result = {"success": False, "message": "不支持动态参数更新"}

        return JsonResponse(result)
    except json.JSONDecodeError:
        return JsonResponse({"success": False, "message": "无效的JSON数据"})
    except Exception as e:
        return JsonResponse({"success": False, "message": f"参数更新失败: {e}"})


@csrf_exempt
def change_time_scale(request):
    """切换仿真时间尺度API端点"""
    if request.method != 'POST':
        return JsonResponse({"success": False, "message": "仅支持POST请求"})

    try:
        data = json.loads(request.body)
        time_scale = data.get('time_scale')

        if not time_scale:
            return JsonResponse({"success": False, "message": "未提供时间尺度参数"})

        valid_scales = ['secondly', 'minutely', 'hourly', 'daily']
        if time_scale not in valid_scales:
            return JsonResponse({
                "success": False,
                "message": f"无效的时间尺度，支持的尺度: {valid_scales}"
            })

        pv_model = PVModelAdapter.get_instance()
        if hasattr(pv_model, 'change_time_scale'):
            result = pv_model.change_time_scale(time_scale)
        else:
            result = {"success": False, "message": "不支持时间尺度切换"}

        return JsonResponse(result)
    except json.JSONDecodeError:
        return JsonResponse({"success": False, "message": "无效的JSON数据"})
    except Exception as e:
        return JsonResponse({"success": False, "message": f"时间尺度切换失败: {e}"})


def advanced_simulation_status(request):
    """获取高级仿真状态API端点"""
    try:
        pv_model = PVModelAdapter.get_instance()
        if hasattr(pv_model, 'get_advanced_simulation_status'):
            status = pv_model.get_advanced_simulation_status()
        else:
            # 回退到基础状态
            status = pv_model.get_simulation_status()
            status["phase4_features"] = {"available": False}

        return JsonResponse(status)
    except Exception as e:
        return JsonResponse({"error": f"获取仿真状态失败: {e}"})


def query_historical_data(request):
    """查询历史仿真数据API端点"""
    try:
        # 获取查询参数
        start_time = request.GET.get('start_time')
        end_time = request.GET.get('end_time')
        data_type = request.GET.get('data_type')
        limit = int(request.GET.get('limit', 100))

        pv_model = PVModelAdapter.get_instance()
        if hasattr(pv_model, 'query_historical_data'):
            result = pv_model.query_historical_data(
                start_time=start_time,
                end_time=end_time,
                data_type=data_type,
                limit=limit
            )
        else:
            result = {"success": False, "message": "不支持历史数据查询", "data": []}

        return JsonResponse(result)
    except ValueError as e:
        return JsonResponse({"success": False, "message": f"参数错误: {e}", "data": []})
    except Exception as e:
        return JsonResponse({"success": False, "message": f"查询失败: {e}", "data": []})


@csrf_exempt
def cleanup_old_data(request):
    """清理过期数据API端点"""
    if request.method != 'POST':
        return JsonResponse({"success": False, "message": "仅支持POST请求"})

    try:
        pv_model = PVModelAdapter.get_instance()
        if hasattr(pv_model, 'cleanup_old_data'):
            result = pv_model.cleanup_old_data()
        else:
            result = {"success": False, "message": "不支持数据清理功能"}

        return JsonResponse(result)
    except Exception as e:
        return JsonResponse({"success": False, "message": f"数据清理失败: {e}"})
