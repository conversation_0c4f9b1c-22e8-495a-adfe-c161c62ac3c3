"""
配置管理模块

提供仿真系统的配置管理功能，支持新旧系统切换。
"""

import os
from django.conf import settings


class SimulationConfig:
    """仿真系统配置管理类"""

    @staticmethod
    def use_real_simulation():
        """
        检查是否使用真实仿真系统

        优先级：
        1. 环境变量 USE_REAL_SIMULATION
        2. Django设置 USE_REAL_SIMULATION
        3. 默认值 False（使用模拟系统）

        Returns:
            bool: True表示使用真实仿真系统，False表示使用模拟系统
        """
        # 环境变量优先级最高
        env_setting = os.getenv('USE_REAL_SIMULATION', '').lower()
        if env_setting in ['true', '1', 'yes', 'on']:
            return True
        elif env_setting in ['false', '0', 'no', 'off']:
            return False

        # Django设置次之
        return getattr(settings, 'USE_REAL_SIMULATION', False)

    @staticmethod
    def get_simulation_config():
        """
        获取仿真系统配置

        Returns:
            dict: 仿真系统配置字典
        """
        return getattr(settings, 'SIMULATION_CONFIG', {
            'USE_REAL_SIMULATION': False,
            'MODEL_PATH': None,
            'CACHE_TIMEOUT': 300,  # 5分钟缓存
            'MAX_SIMULATION_POINTS': 180,  # 最大数据点数
            'RESPONSE_TIME_LIMIT': 2.0,  # 响应时间限制（秒）
            'SIMULATION_CONTROL': {
                'ENABLE_PAUSE_RESUME': True,  # 启用暂停/恢复功能
                'ENABLE_DYNAMIC_PARAMS': True,  # 启用动态参数调整
                'AUTO_SAVE_INTERVAL': 3600,  # 自动保存间隔（秒）
                'MAX_MEMORY_USAGE_MB': 512,  # 最大内存使用（MB）
                'MULTI_TIME_SCALE': {
                    'ENABLE': True,  # 启用多时间尺度仿真
                    'DEFAULT_SCALE': 'hourly',  # 默认时间尺度
                    'AVAILABLE_SCALES': ['secondly', 'minutely', 'hourly', 'daily']
                }
            },
            'DATA_PERSISTENCE': {
                'ENABLE': True,  # 启用数据持久化
                'STORAGE_TYPE': 'database',  # 存储类型：database, file, memory
                'MAX_HISTORY_DAYS': 30,  # 最大历史数据保存天数
                'COMPRESSION': True,  # 启用数据压缩
                'BACKUP_INTERVAL': 86400,  # 备份间隔（秒）
            }
        })

    @staticmethod
    def emergency_fallback():
        """
        紧急回滚到模拟系统

        设置环境变量强制使用模拟系统，并清除可能的缓存
        """
        os.environ['USE_REAL_SIMULATION'] = 'false'
        print("紧急回滚：已切换到模拟仿真系统")

        # 清除单例实例，强制重新初始化
        try:
            from .pv_model_adapter import PVModelAdapter
            PVModelAdapter._instance = None
            print("已清除PVModelAdapter单例实例")
        except ImportError:
            pass

    @staticmethod
    def get_src_path():
        """
        获取src目录的绝对路径

        Returns:
            str: src目录的绝对路径
        """
        # 从Django项目根目录向上一级找到src目录
        base_dir = getattr(settings, 'BASE_DIR', None)
        if base_dir:
            src_path = base_dir.parent / 'src'
            return str(src_path)

        # 备用方案：相对路径
        return os.path.join(os.path.dirname(__file__), '..', '..', '..', 'src')

    @staticmethod
    def validate_src_modules():
        """
        验证src模块是否可以正常导入

        Returns:
            tuple: (success: bool, message: str)
        """
        try:
            import sys
            src_path = SimulationConfig.get_src_path()
            if src_path not in sys.path:
                sys.path.insert(0, src_path)  # 使用insert(0, ...)确保优先级

            # 尝试导入关键模块
            import model.pv_model
            import data_layer.meteorological_data_module
            import model.anomaly_model
            import simulation_layer.time_series_simulation_engine

            # 验证关键类是否存在
            from model.pv_model import PVDigitalTwin
            from data_layer.meteorological_data_module import MeteorologicalDataModule
            from model.anomaly_model import AnomalyModel
            from simulation_layer.time_series_simulation_engine import SimulationEngine

            return True, "所有src模块导入成功"

        except ImportError as e:
            return False, f"导入src模块失败: {str(e)}"
        except Exception as e:
            return False, f"验证src模块时发生错误: {str(e)}"

    @staticmethod
    def get_model_path():
        """
        获取模型文件路径

        Returns:
            str: 模型文件目录路径
        """
        config = SimulationConfig.get_simulation_config()
        model_path = config.get('MODEL_PATH')

        if model_path:
            return str(model_path)

        # 默认路径：项目根目录下的models文件夹
        base_dir = getattr(settings, 'BASE_DIR', None)
        if base_dir:
            return str(base_dir.parent / 'models')

        return os.path.join(os.path.dirname(__file__), '..', '..', '..', 'models')


class ConfigurationError(Exception):
    """配置错误异常"""
    pass


def get_adapter_class():
    """
    根据配置获取适当的适配器类

    Returns:
        class: PVModelAdapter类或其子类
    """
    if SimulationConfig.use_real_simulation():
        try:
            # 验证src模块是否可用
            success, message = SimulationConfig.validate_src_modules()
            if not success:
                print(f"警告: {message}")
                print("回滚到模拟系统")
                SimulationConfig.emergency_fallback()
                from .pv_model_adapter import PVModelAdapter
                return PVModelAdapter

            # 导入真实适配器
            from .real_pv_adapter import RealPVModelAdapter
            print("使用真实仿真系统")
            return RealPVModelAdapter

        except ImportError as e:
            print(f"警告: 无法导入真实适配器: {e}")
            print("回滚到模拟系统")
            SimulationConfig.emergency_fallback()
            from .pv_model_adapter import PVModelAdapter
            return PVModelAdapter
    else:
        print("使用模拟仿真系统")
        from .pv_model_adapter import PVModelAdapter
        return PVModelAdapter
