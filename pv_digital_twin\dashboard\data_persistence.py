"""
数据持久化模块

提供仿真结果的持久化存储、历史数据查询和数据备份功能。
"""

import os
import json
import pickle
import gzip
import sqlite3
import pandas as pd
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union
from pathlib import Path
from .config import SimulationConfig

logger = logging.getLogger(__name__)


class DataPersistence:
    """
    数据持久化管理类
    
    支持多种存储方式：数据库、文件、内存缓存
    """
    
    def __init__(self):
        """初始化数据持久化管理器"""
        self.config = SimulationConfig.get_simulation_config()
        self.persistence_config = self.config.get('DATA_PERSISTENCE', {})
        
        self.enabled = self.persistence_config.get('ENABLE', True)
        self.storage_type = self.persistence_config.get('STORAGE_TYPE', 'database')
        self.max_history_days = self.persistence_config.get('MAX_HISTORY_DAYS', 30)
        self.compression = self.persistence_config.get('COMPRESSION', True)
        
        # 存储路径
        self.data_dir = Path('data/simulation_results')
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # 数据库连接
        self.db_path = self.data_dir / 'simulation_results.db'
        self.db_connection = None
        
        # 内存缓存
        self.memory_cache = {}
        self.cache_size_limit = 1000  # 最大缓存条目数
        
        if self.enabled:
            self._initialize_storage()
        
        logger.info(f"数据持久化初始化完成，存储类型: {self.storage_type}")
    
    def _initialize_storage(self):
        """初始化存储系统"""
        if self.storage_type == 'database':
            self._initialize_database()
        elif self.storage_type == 'file':
            self._initialize_file_storage()
        elif self.storage_type == 'memory':
            self._initialize_memory_storage()
    
    def _initialize_database(self):
        """初始化数据库存储"""
        try:
            self.db_connection = sqlite3.connect(str(self.db_path), check_same_thread=False)
            
            # 创建表结构
            cursor = self.db_connection.cursor()
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS simulation_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME NOT NULL,
                    simulation_time REAL NOT NULL,
                    data_type TEXT NOT NULL,
                    data_json TEXT NOT NULL,
                    compressed BOOLEAN DEFAULT FALSE,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_timestamp 
                ON simulation_results(timestamp)
            ''')
            
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_simulation_time 
                ON simulation_results(simulation_time)
            ''')
            
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_data_type 
                ON simulation_results(data_type)
            ''')
            
            self.db_connection.commit()
            logger.info("数据库初始化完成")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            self.storage_type = 'file'  # 回退到文件存储
            self._initialize_file_storage()
    
    def _initialize_file_storage(self):
        """初始化文件存储"""
        self.file_storage_dir = self.data_dir / 'files'
        self.file_storage_dir.mkdir(exist_ok=True)
        logger.info("文件存储初始化完成")
    
    def _initialize_memory_storage(self):
        """初始化内存存储"""
        self.memory_cache.clear()
        logger.info("内存存储初始化完成")
    
    def save_simulation_result(self, simulation_time: float, data: Dict[str, Any], 
                             data_type: str = 'simulation_step') -> bool:
        """
        保存仿真结果
        
        Args:
            simulation_time: 仿真时间
            data: 仿真数据
            data_type: 数据类型
            
        Returns:
            bool: 保存是否成功
        """
        if not self.enabled:
            return True
        
        try:
            timestamp = datetime.now()
            
            if self.storage_type == 'database':
                return self._save_to_database(timestamp, simulation_time, data, data_type)
            elif self.storage_type == 'file':
                return self._save_to_file(timestamp, simulation_time, data, data_type)
            elif self.storage_type == 'memory':
                return self._save_to_memory(timestamp, simulation_time, data, data_type)
            
        except Exception as e:
            logger.error(f"保存仿真结果失败: {e}")
            return False
    
    def _save_to_database(self, timestamp: datetime, simulation_time: float, 
                         data: Dict[str, Any], data_type: str) -> bool:
        """保存到数据库"""
        try:
            data_json = json.dumps(data, default=str)
            
            # 压缩数据（如果启用）
            compressed = False
            if self.compression and len(data_json) > 1024:  # 大于1KB时压缩
                data_json = gzip.compress(data_json.encode()).decode('latin1')
                compressed = True
            
            cursor = self.db_connection.cursor()
            cursor.execute('''
                INSERT INTO simulation_results 
                (timestamp, simulation_time, data_type, data_json, compressed)
                VALUES (?, ?, ?, ?, ?)
            ''', (timestamp, simulation_time, data_type, data_json, compressed))
            
            self.db_connection.commit()
            return True
            
        except Exception as e:
            logger.error(f"数据库保存失败: {e}")
            return False
    
    def _save_to_file(self, timestamp: datetime, simulation_time: float, 
                     data: Dict[str, Any], data_type: str) -> bool:
        """保存到文件"""
        try:
            # 按日期组织文件
            date_str = timestamp.strftime('%Y-%m-%d')
            file_dir = self.file_storage_dir / date_str
            file_dir.mkdir(exist_ok=True)
            
            # 文件名包含时间戳和数据类型
            filename = f"{timestamp.strftime('%H-%M-%S')}_{data_type}_{simulation_time:.2f}.json"
            file_path = file_dir / filename
            
            # 保存数据
            save_data = {
                'timestamp': timestamp.isoformat(),
                'simulation_time': simulation_time,
                'data_type': data_type,
                'data': data
            }
            
            if self.compression:
                with gzip.open(f"{file_path}.gz", 'wt', encoding='utf-8') as f:
                    json.dump(save_data, f, default=str, indent=2)
            else:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(save_data, f, default=str, indent=2)
            
            return True
            
        except Exception as e:
            logger.error(f"文件保存失败: {e}")
            return False
    
    def _save_to_memory(self, timestamp: datetime, simulation_time: float, 
                       data: Dict[str, Any], data_type: str) -> bool:
        """保存到内存"""
        try:
            key = f"{timestamp.isoformat()}_{data_type}_{simulation_time}"
            
            # 检查缓存大小限制
            if len(self.memory_cache) >= self.cache_size_limit:
                # 删除最旧的条目
                oldest_key = min(self.memory_cache.keys())
                del self.memory_cache[oldest_key]
            
            self.memory_cache[key] = {
                'timestamp': timestamp,
                'simulation_time': simulation_time,
                'data_type': data_type,
                'data': data
            }
            
            return True
            
        except Exception as e:
            logger.error(f"内存保存失败: {e}")
            return False
    
    def query_historical_data(self, start_time: Optional[datetime] = None, 
                            end_time: Optional[datetime] = None,
                            data_type: Optional[str] = None,
                            limit: int = 1000) -> List[Dict[str, Any]]:
        """
        查询历史数据
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            data_type: 数据类型过滤
            limit: 最大返回条目数
            
        Returns:
            list: 历史数据列表
        """
        if not self.enabled:
            return []
        
        try:
            if self.storage_type == 'database':
                return self._query_from_database(start_time, end_time, data_type, limit)
            elif self.storage_type == 'file':
                return self._query_from_file(start_time, end_time, data_type, limit)
            elif self.storage_type == 'memory':
                return self._query_from_memory(start_time, end_time, data_type, limit)
            
        except Exception as e:
            logger.error(f"查询历史数据失败: {e}")
            return []
    
    def _query_from_database(self, start_time: Optional[datetime], 
                           end_time: Optional[datetime],
                           data_type: Optional[str], limit: int) -> List[Dict[str, Any]]:
        """从数据库查询"""
        try:
            cursor = self.db_connection.cursor()
            
            # 构建查询条件
            conditions = []
            params = []
            
            if start_time:
                conditions.append("timestamp >= ?")
                params.append(start_time)
            
            if end_time:
                conditions.append("timestamp <= ?")
                params.append(end_time)
            
            if data_type:
                conditions.append("data_type = ?")
                params.append(data_type)
            
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            
            query = f'''
                SELECT timestamp, simulation_time, data_type, data_json, compressed
                FROM simulation_results
                WHERE {where_clause}
                ORDER BY timestamp DESC
                LIMIT ?
            '''
            params.append(limit)
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            results = []
            for row in rows:
                timestamp, simulation_time, data_type, data_json, compressed = row
                
                # 解压缩数据（如果需要）
                if compressed:
                    data_json = gzip.decompress(data_json.encode('latin1')).decode()
                
                data = json.loads(data_json)
                
                results.append({
                    'timestamp': datetime.fromisoformat(timestamp),
                    'simulation_time': simulation_time,
                    'data_type': data_type,
                    'data': data
                })
            
            return results
            
        except Exception as e:
            logger.error(f"数据库查询失败: {e}")
            return []
    
    def _query_from_file(self, start_time: Optional[datetime], 
                        end_time: Optional[datetime],
                        data_type: Optional[str], limit: int) -> List[Dict[str, Any]]:
        """从文件查询"""
        try:
            results = []
            
            # 遍历日期目录
            for date_dir in sorted(self.file_storage_dir.iterdir(), reverse=True):
                if not date_dir.is_dir():
                    continue
                
                # 检查日期范围
                try:
                    date = datetime.strptime(date_dir.name, '%Y-%m-%d')
                    if start_time and date < start_time.replace(hour=0, minute=0, second=0):
                        continue
                    if end_time and date > end_time.replace(hour=23, minute=59, second=59):
                        continue
                except ValueError:
                    continue
                
                # 遍历文件
                for file_path in sorted(date_dir.iterdir(), reverse=True):
                    if len(results) >= limit:
                        break
                    
                    try:
                        # 加载数据
                        if file_path.suffix == '.gz':
                            with gzip.open(file_path, 'rt', encoding='utf-8') as f:
                                data = json.load(f)
                        else:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                data = json.load(f)
                        
                        # 过滤条件
                        timestamp = datetime.fromisoformat(data['timestamp'])
                        if start_time and timestamp < start_time:
                            continue
                        if end_time and timestamp > end_time:
                            continue
                        if data_type and data['data_type'] != data_type:
                            continue
                        
                        results.append(data)
                        
                    except Exception as e:
                        logger.warning(f"读取文件失败 {file_path}: {e}")
                        continue
                
                if len(results) >= limit:
                    break
            
            return results
            
        except Exception as e:
            logger.error(f"文件查询失败: {e}")
            return []
    
    def _query_from_memory(self, start_time: Optional[datetime], 
                          end_time: Optional[datetime],
                          data_type: Optional[str], limit: int) -> List[Dict[str, Any]]:
        """从内存查询"""
        try:
            results = []
            
            # 按时间戳排序
            sorted_items = sorted(self.memory_cache.items(), 
                                key=lambda x: x[1]['timestamp'], reverse=True)
            
            for key, data in sorted_items:
                if len(results) >= limit:
                    break
                
                timestamp = data['timestamp']
                
                # 过滤条件
                if start_time and timestamp < start_time:
                    continue
                if end_time and timestamp > end_time:
                    continue
                if data_type and data['data_type'] != data_type:
                    continue
                
                results.append(data)
            
            return results
            
        except Exception as e:
            logger.error(f"内存查询失败: {e}")
            return []
    
    def cleanup_old_data(self) -> bool:
        """
        清理过期数据
        
        Returns:
            bool: 清理是否成功
        """
        if not self.enabled:
            return True
        
        try:
            cutoff_date = datetime.now() - timedelta(days=self.max_history_days)
            
            if self.storage_type == 'database':
                return self._cleanup_database(cutoff_date)
            elif self.storage_type == 'file':
                return self._cleanup_files(cutoff_date)
            elif self.storage_type == 'memory':
                return self._cleanup_memory(cutoff_date)
            
        except Exception as e:
            logger.error(f"清理过期数据失败: {e}")
            return False
    
    def _cleanup_database(self, cutoff_date: datetime) -> bool:
        """清理数据库中的过期数据"""
        try:
            cursor = self.db_connection.cursor()
            cursor.execute("DELETE FROM simulation_results WHERE timestamp < ?", (cutoff_date,))
            deleted_count = cursor.rowcount
            self.db_connection.commit()
            
            logger.info(f"数据库清理完成，删除了 {deleted_count} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"数据库清理失败: {e}")
            return False
    
    def _cleanup_files(self, cutoff_date: datetime) -> bool:
        """清理过期文件"""
        try:
            deleted_count = 0
            
            for date_dir in self.file_storage_dir.iterdir():
                if not date_dir.is_dir():
                    continue
                
                try:
                    date = datetime.strptime(date_dir.name, '%Y-%m-%d')
                    if date < cutoff_date:
                        # 删除整个日期目录
                        import shutil
                        shutil.rmtree(date_dir)
                        deleted_count += 1
                except ValueError:
                    continue
            
            logger.info(f"文件清理完成，删除了 {deleted_count} 个日期目录")
            return True
            
        except Exception as e:
            logger.error(f"文件清理失败: {e}")
            return False
    
    def _cleanup_memory(self, cutoff_date: datetime) -> bool:
        """清理内存中的过期数据"""
        try:
            keys_to_delete = []
            
            for key, data in self.memory_cache.items():
                if data['timestamp'] < cutoff_date:
                    keys_to_delete.append(key)
            
            for key in keys_to_delete:
                del self.memory_cache[key]
            
            logger.info(f"内存清理完成，删除了 {len(keys_to_delete)} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"内存清理失败: {e}")
            return False
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """
        获取存储统计信息
        
        Returns:
            dict: 存储统计信息
        """
        stats = {
            'enabled': self.enabled,
            'storage_type': self.storage_type,
            'max_history_days': self.max_history_days,
            'compression': self.compression
        }
        
        try:
            if self.storage_type == 'database' and self.db_connection:
                cursor = self.db_connection.cursor()
                cursor.execute("SELECT COUNT(*) FROM simulation_results")
                stats['total_records'] = cursor.fetchone()[0]
                
                cursor.execute("SELECT MIN(timestamp), MAX(timestamp) FROM simulation_results")
                min_time, max_time = cursor.fetchone()
                stats['time_range'] = {'min': min_time, 'max': max_time}
                
            elif self.storage_type == 'file':
                total_files = sum(1 for date_dir in self.file_storage_dir.iterdir() 
                                if date_dir.is_dir() 
                                for file_path in date_dir.iterdir() 
                                if file_path.is_file())
                stats['total_files'] = total_files
                
            elif self.storage_type == 'memory':
                stats['total_records'] = len(self.memory_cache)
                stats['cache_size_limit'] = self.cache_size_limit
                
        except Exception as e:
            logger.error(f"获取存储统计失败: {e}")
            stats['error'] = str(e)
        
        return stats
    
    def close(self):
        """关闭数据持久化管理器"""
        if self.db_connection:
            self.db_connection.close()
            self.db_connection = None
        
        logger.info("数据持久化管理器已关闭")
