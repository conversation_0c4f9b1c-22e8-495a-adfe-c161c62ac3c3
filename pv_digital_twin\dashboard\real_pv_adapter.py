"""
真实PV适配器模块

封装src/model/pv_model.py中的PVDigitalTwin类，提供与Django应用兼容的接口。
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
from typing import Dict, List, Any, Optional, Union
import threading
import time
import logging

# 导入配置和转换模块
from .config import SimulationConfig
from .data_format_converter import DataFormatConverter
from .weather_data_bridge import WeatherDataBridge
from .simulation_controller import SimulationController
from .data_persistence import DataPersistence

# 设置日志
logger = logging.getLogger(__name__)


class RealPVModelAdapter:
    """真实PV模型适配器类"""

    _instance = None
    _simulation_logs = []
    _is_paused = False
    _is_running = False
    _continuous_simulation_active = False
    _last_anomaly_check_time = None
    _anomaly_cache_duration = 10

    @classmethod
    def get_instance(cls):
        """单例模式获取实例"""
        if cls._instance is None:
            cls._instance = RealPVModelAdapter()
        return cls._instance

    def __init__(self):
        """初始化真实PV模型适配器"""
        if RealPVModelAdapter._instance is not None:
            raise Exception("这是一个单例类，请使用get_instance()方法")

        self.pv_twin = None
        self.anomaly_model = None
        self.simulation_engine = None
        self.weather_bridge = None
        self.weather_data = None
        self.simulation_results = None

        # 阶段四新增组件
        self.simulation_controller = None
        self.data_persistence = None

        # 初始化组件
        self._initialize_src_modules()
        self._initialize_pv_twin()
        self._initialize_anomaly_model()
        self._initialize_weather_bridge()
        self._initialize_phase4_components()  # 阶段四组件初始化
        self._generate_initial_weather_data()
        self._run_initial_simulation()
        self._start_continuous_simulation()

    def _initialize_src_modules(self):
        """初始化src模块"""
        try:
            # 确保src路径在sys.path中
            src_path = SimulationConfig.get_src_path()
            if src_path not in sys.path:
                sys.path.append(src_path)

            # 验证模块可用性
            success, message = SimulationConfig.validate_src_modules()
            if not success:
                raise ImportError(f"src模块验证失败: {message}")

            self._log_message("src模块初始化成功")

        except Exception as e:
            self._log_message(f"src模块初始化失败: {e}")
            raise

    def _initialize_pv_twin(self):
        """初始化PV数字孪生模型"""
        try:
            from model.pv_model import PVDigitalTwin

            # 默认系统参数
            self.pv_twin = PVDigitalTwin(
                latitude=39.9,  # 北京
                longitude=116.4,
                altitude=50,
                name="Real PV Digital Twin System",
                module_parameters={
                    "pdc0": 300.0,  # 单个模块功率300W
                    "gamma_pdc": -0.004,  # 温度系数-0.4%/°C
                },
                inverter_parameters={
                    "pdc0": 5000.0  # 系统总功率5kW
                },
                modules_per_string=10,
                strings_per_inverter=2,
                system_loss_dc_ohmic=0.02,  # 2%直流损耗
                transformer_efficiency=0.985
            )

            self._log_message(f"PV数字孪生模型初始化成功: {self.pv_twin.location.name}")
            self._log_message(f"位置: 纬度={self.pv_twin.location.latitude}, 经度={self.pv_twin.location.longitude}")
            self._log_message(f"系统容量: {self.pv_twin.inverter_parameters_dict.get('pdc0', 0)/1000:.2f} kWp")

        except Exception as e:
            self._log_message(f"PV数字孪生模型初始化失败: {e}")
            raise

    def _initialize_anomaly_model(self):
        """初始化异常检测模型"""
        try:
            from model.anomaly_model import AnomalyModel

            # 使用改进的AnomalyModel，支持自动查找模型路径
            self.anomaly_model = AnomalyModel(
                pv_digital_twin_model=self.pv_twin,
                model_path=None,  # 让AnomalyModel自动查找模型文件
                initial_threshold=200.0  # 设置初始阈值
            )

            # 检查模型是否成功加载
            if self.anomaly_model.detection_model is not None:
                self._log_message("LSTM+KAN异常检测模型加载成功")
            else:
                self._log_message("警告: LSTM+KAN模型未加载，将使用基础异常检测")

            # 设置异常检测阈值
            self.anomaly_model.set_threshold(200)
            self._log_message(f"异常检测阈值设置为: {self.anomaly_model.threshold}")

            self._log_message("异常检测模型初始化成功")

        except Exception as e:
            self._log_message(f"异常检测模型初始化失败: {e}")
            # 异常检测模型是可选的，失败时设为None
            self.anomaly_model = None

    def _initialize_weather_bridge(self):
        """初始化天气数据桥接器和气象数据模块"""
        try:
            # 初始化天气数据桥接器
            self.weather_bridge = WeatherDataBridge()
            self._log_message("天气数据桥接器初始化成功")

            # 尝试初始化气象数据模块
            try:
                from data_layer.meteorological_data_module import MeteorologicalDataModule

                self.weather_module = MeteorologicalDataModule()
                self._log_message("气象数据模块初始化成功")

                # 设置位置信息
                if hasattr(self.weather_module, 'set_location'):
                    self.weather_module.set_location(
                        latitude=self.pv_twin.location.latitude,
                        longitude=self.pv_twin.location.longitude,
                        altitude=self.pv_twin.location.altitude
                    )

            except ImportError as e:
                self._log_message(f"警告: 无法导入气象数据模块: {e}")
                self.weather_module = None
            except Exception as e:
                self._log_message(f"气象数据模块初始化失败: {e}")
                self.weather_module = None

        except Exception as e:
            self._log_message(f"天气数据桥接器初始化失败: {e}")
            self.weather_bridge = None
            self.weather_module = None

    def _initialize_phase4_components(self):
        """初始化阶段四新增组件"""
        try:
            # 初始化数据持久化管理器
            self.data_persistence = DataPersistence()
            self._log_message("数据持久化管理器初始化成功")

            # 初始化仿真控制器（稍后设置仿真引擎）
            self.simulation_controller = SimulationController()
            self._log_message("仿真控制器初始化成功")

        except Exception as e:
            self._log_message(f"阶段四组件初始化失败: {e}")
            # 这些组件是可选的，失败时设为None
            self.data_persistence = None
            self.simulation_controller = None

    def _generate_initial_weather_data(self):
        """生成初始天气数据"""
        try:
            # 生成48小时的天气数据
            start_time = datetime.now()
            end_time = start_time + timedelta(hours=48)

            # 优先使用气象数据模块
            if self.weather_module:
                try:
                    # 使用真实的气象数据模块生成数据
                    times = pd.date_range(start=start_time, end=end_time, freq='H')

                    # 生成合成天气数据
                    self.weather_data = self.weather_module.generate_synthetic_weather_data(
                        times=times,
                        location={
                            'latitude': self.pv_twin.location.latitude,
                            'longitude': self.pv_twin.location.longitude,
                            'altitude': self.pv_twin.location.altitude
                        }
                    )

                    self._log_message(f"使用气象数据模块生成天气数据: {len(self.weather_data)}条记录")
                    return

                except Exception as e:
                    self._log_message(f"气象数据模块生成数据失败: {e}，使用备用方法")

            # 使用天气数据桥接器
            if self.weather_bridge:
                self.weather_data = self.weather_bridge.generate_weather_data(
                    start_time=start_time,
                    end_time=end_time,
                    freq='H',
                    latitude=self.pv_twin.location.latitude,
                    longitude=self.pv_twin.location.longitude,
                    altitude=self.pv_twin.location.altitude
                )
                self._log_message(f"使用天气数据桥接器生成数据: {len(self.weather_data)}条记录")
            else:
                # 备用天气数据生成
                self.weather_data = self._generate_fallback_weather_data(start_time, end_time)
                self._log_message(f"使用备用方法生成天气数据: {len(self.weather_data)}条记录")

        except Exception as e:
            self._log_message(f"天气数据生成失败: {e}")
            # 生成最基本的天气数据
            self.weather_data = self._generate_fallback_weather_data(
                datetime.now(),
                datetime.now() + timedelta(hours=48)
            )

    def _generate_fallback_weather_data(self, start_time: datetime, end_time: datetime) -> pd.DataFrame:
        """生成备用天气数据"""
        times = pd.date_range(start=start_time, end=end_time, freq='H')
        return pd.DataFrame({
            'ghi': [500] * len(times),
            'dni': [400] * len(times),
            'dhi': [100] * len(times),
            'temp_air': [25] * len(times),
            'wind_speed': [2] * len(times)
        }, index=times)

    def _run_initial_simulation(self):
        """运行初始仿真"""
        try:
            # 优先使用直接的pvlib ModelChain计算
            if hasattr(self.pv_twin, 'model') and self.pv_twin.model is not None:
                try:
                    self._run_pvlib_simulation()
                    self._log_message(f"使用pvlib ModelChain完成初始仿真，共{len(self.simulation_results)}个时间点")
                    return
                except Exception as e:
                    self._log_message(f"pvlib ModelChain仿真失败: {e}，尝试使用仿真引擎")

            # 备用方案：使用仿真引擎
            try:
                from simulation_layer.time_series_simulation_engine import SimulationEngine

                # 创建仿真引擎
                self.simulation_engine = SimulationEngine(self.pv_twin, self.anomaly_model)

                # 设置天气数据
                self.simulation_engine.weather_data_for_simulation = self.weather_data

                # 配置仿真参数
                self.simulation_engine.configure_simulation(
                    start_time=0,
                    end_time=len(self.weather_data) - 1,
                    time_step_seconds=3600
                )

                # 阶段四增强：集成仿真控制器
                if self.simulation_controller:
                    self.simulation_controller.set_simulation_engine(self.simulation_engine)
                    # 使用控制器启动仿真
                    success = self.simulation_controller.start_simulation()
                    if not success:
                        # 如果控制器启动失败，直接启动仿真引擎
                        self.simulation_engine.start()
                else:
                    # 直接启动仿真引擎
                    self.simulation_engine.start()

                # 处理仿真结果
                self.simulation_results = self._process_simulation_results()

                self._log_message(f"使用仿真引擎完成初始仿真，共{len(self.simulation_engine.model_states)}个时间点")

            except ImportError as e:
                self._log_message(f"无法导入仿真引擎: {e}，使用简化仿真")
                self._run_simplified_simulation()

        except Exception as e:
            self._log_message(f"初始仿真失败: {e}")
            # 创建空的仿真结果
            self.simulation_results = pd.DataFrame()

    def _process_simulation_results(self) -> pd.DataFrame:
        """处理仿真结果"""
        try:
            if not hasattr(self.simulation_engine, 'model_states') or not self.simulation_engine.model_states:
                return pd.DataFrame()

            # 将仿真结果转换为DataFrame
            results_list = []
            for timestamp, state in self.simulation_engine.model_states.items():
                if isinstance(state, dict) and 'error' not in state:
                    results_list.append(state)

            if not results_list:
                return pd.DataFrame()

            results_df = pd.DataFrame(results_list)

            # 确保有datetime列
            if 'datetime' in results_df.columns:
                results_df['datetime'] = pd.to_datetime(results_df['datetime'])
            else:
                # 使用天气数据的时间索引
                results_df['datetime'] = self.weather_data.index[:len(results_df)]

            return results_df

        except Exception as e:
            self._log_message(f"仿真结果处理失败: {e}")
            return pd.DataFrame()

    def _run_pvlib_simulation(self):
        """使用pvlib ModelChain直接运行仿真"""
        try:
            if self.weather_data is None or self.weather_data.empty:
                raise ValueError("天气数据为空")

            # 确保天气数据格式正确
            weather_df = self.weather_data.copy()

            # 确保有必要的列
            required_cols = ['ghi', 'temp_air', 'wind_speed']
            for col in required_cols:
                if col not in weather_df.columns:
                    if col == 'ghi':
                        weather_df[col] = 500  # 默认辐照度
                    elif col == 'temp_air':
                        weather_df[col] = 25   # 默认温度
                    elif col == 'wind_speed':
                        weather_df[col] = 2    # 默认风速

            # 如果缺少DNI和DHI，从GHI估算
            if 'dni' not in weather_df.columns or 'dhi' not in weather_df.columns:
                weather_df['dni'] = weather_df['ghi'] * 0.8
                weather_df['dhi'] = weather_df['ghi'] * 0.2

            # 运行ModelChain
            self.pv_twin.model.run_model(weather=weather_df)

            # 提取结果
            results_data = []
            for i, timestamp in enumerate(weather_df.index):
                result = {
                    'datetime': timestamp,
                    'ghi': weather_df.loc[timestamp, 'ghi'],
                    'temp_air': weather_df.loc[timestamp, 'temp_air'],
                    'wind_speed': weather_df.loc[timestamp, 'wind_speed'],
                    'temp_cell': getattr(self.pv_twin.model.results, 'cell_temperature', [25] * len(weather_df))[i] if hasattr(self.pv_twin.model.results, 'cell_temperature') else 25,
                    'dc_power': getattr(self.pv_twin.model.results, 'dc', [0] * len(weather_df))[i] if hasattr(self.pv_twin.model.results, 'dc') else 0,
                    'ac_power': getattr(self.pv_twin.model.results, 'ac', [0] * len(weather_df))[i] if hasattr(self.pv_twin.model.results, 'ac') else 0,
                }
                results_data.append(result)

            self.simulation_results = pd.DataFrame(results_data)
            self._log_message("pvlib ModelChain仿真完成")

        except Exception as e:
            self._log_message(f"pvlib ModelChain仿真错误: {e}")
            raise

    def _run_simplified_simulation(self):
        """运行简化仿真（备用方案）"""
        try:
            if self.weather_data is None or self.weather_data.empty:
                raise ValueError("天气数据为空")

            results_data = []
            for timestamp, weather_row in self.weather_data.iterrows():
                # 使用PVDigitalTwin的单步仿真方法
                result = self.pv_twin.run_single_simulation_step(
                    timestamp=timestamp.timestamp(),
                    weather_for_step=weather_row
                )

                # 添加时间戳
                result['datetime'] = timestamp
                results_data.append(result)

            self.simulation_results = pd.DataFrame(results_data)
            self._log_message("简化仿真完成")

        except Exception as e:
            self._log_message(f"简化仿真错误: {e}")
            # 创建基本的仿真结果
            self.simulation_results = pd.DataFrame({
                'datetime': self.weather_data.index,
                'ac_power': [0] * len(self.weather_data),
                'dc_power': [0] * len(self.weather_data),
                'temp_air': self.weather_data.get('temp_air', [25] * len(self.weather_data)),
                'temp_cell': [25] * len(self.weather_data),
                'ghi': self.weather_data.get('ghi', [0] * len(self.weather_data))
            })

    def _start_continuous_simulation(self):
        """启动持续仿真"""
        try:
            self._continuous_simulation_thread = threading.Thread(
                target=self._continuous_simulation,
                daemon=True
            )
            self._continuous_simulation_active = True
            self._is_running = True
            self._is_paused = False
            self._continuous_simulation_thread.start()

            self._log_message("持续仿真已启动")

        except Exception as e:
            self._log_message(f"持续仿真启动失败: {e}")

    def _continuous_simulation(self):
        """持续仿真函数"""
        while self._continuous_simulation_active:
            try:
                if not self._is_paused:
                    # 每小时更新一次仿真数据
                    self._update_simulation_data()

                # 等待一段时间再进行下次更新
                time.sleep(3600)  # 1小时

            except Exception as e:
                self._log_message(f"持续仿真错误: {e}")
                time.sleep(60)  # 出错时等待1分钟再重试

    def _update_simulation_data(self):
        """更新仿真数据"""
        try:
            current_time = datetime.now()

            # 优先使用气象数据模块生成新数据
            if self.weather_module:
                try:
                    times = pd.date_range(start=current_time, end=current_time + timedelta(hours=1), freq='H')
                    new_weather = self.weather_module.generate_synthetic_weather_data(
                        times=times,
                        location={
                            'latitude': self.pv_twin.location.latitude,
                            'longitude': self.pv_twin.location.longitude,
                            'altitude': self.pv_twin.location.altitude
                        }
                    )
                except Exception as e:
                    self._log_message(f"气象数据模块更新失败: {e}，使用备用方法")
                    new_weather = None
            else:
                new_weather = None

            # 备用方案：使用天气数据桥接器
            if new_weather is None or new_weather.empty:
                if self.weather_bridge:
                    new_weather = self.weather_bridge.generate_weather_data(
                        start_time=current_time,
                        end_time=current_time + timedelta(hours=1),
                        freq='H',
                        latitude=self.pv_twin.location.latitude,
                        longitude=self.pv_twin.location.longitude
                    )
                else:
                    # 最后备用方案：生成简单的天气数据
                    new_weather = pd.DataFrame({
                        'ghi': [500],
                        'temp_air': [25],
                        'wind_speed': [2],
                        'dni': [400],
                        'dhi': [100]
                    }, index=pd.DatetimeIndex([current_time]))

            # 运行单步仿真
            if len(new_weather) > 0:
                result = self.pv_twin.run_single_simulation_step(
                    timestamp=current_time.timestamp(),
                    weather_for_step=new_weather.iloc[0]
                )

                # 添加时间戳
                result['datetime'] = current_time

                # 运行异常检测
                if self.anomaly_model and hasattr(self.anomaly_model, 'detect_and_apply_anomaly'):
                    try:
                        # 获取当前PV系统状态用于异常检测
                        current_pv_state = self.pv_twin.get_current_state_for_anomaly_detection(
                            weather_for_step=new_weather.iloc[0]
                        )
                        current_pv_state['datetime'] = current_time

                        # 运行异常检测
                        anomaly_result = self.anomaly_model.detect_and_apply_anomaly(
                            current_pv_state, new_weather.iloc[0].to_dict()
                        )

                        if anomaly_result:
                            self._log_message(f"检测到异常: {anomaly_result.get('anomaly_type', 'UNKNOWN')} "
                                            f"(严重程度: {anomaly_result.get('severity', 0.0):.2f})")
                    except Exception as e:
                        self._log_message(f"异常检测失败: {e}")

                # 阶段四增强：保存仿真结果到持久化存储
                if self.data_persistence:
                    try:
                        self.data_persistence.save_simulation_result(
                            simulation_time=current_time.timestamp(),
                            data=result,
                            data_type='continuous_simulation'
                        )
                    except Exception as e:
                        self._log_message(f"数据持久化保存失败: {e}")

                # 更新仿真结果
                if self.simulation_results is not None and not self.simulation_results.empty:
                    new_result_df = pd.DataFrame([result])
                    self.simulation_results = pd.concat([self.simulation_results, new_result_df], ignore_index=True)

                    # 保持最近48小时的数据
                    if len(self.simulation_results) > 48:
                        self.simulation_results = self.simulation_results.tail(48)
                else:
                    # 如果仿真结果为空，创建新的DataFrame
                    self.simulation_results = pd.DataFrame([result])

        except Exception as e:
            self._log_message(f"仿真数据更新失败: {e}")

    def _log_message(self, message: str):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        RealPVModelAdapter._simulation_logs.append(log_entry)
        print(log_entry)

        # 保持日志数量在合理范围内
        if len(RealPVModelAdapter._simulation_logs) > 1000:
            RealPVModelAdapter._simulation_logs = RealPVModelAdapter._simulation_logs[-500:]

    # ==================== Django API兼容方法 ====================

    def get_simulation_data(self) -> Dict[str, List]:
        """
        获取仿真数据用于图表（Django API兼容）

        Returns:
            dict: 包含时间戳和各种数据的字典
        """
        try:
            if self.simulation_results is None or self.simulation_results.empty:
                return self._get_empty_simulation_data()

            # 获取最近48小时的数据
            recent_data = self.simulation_results.tail(48).copy()

            # 使用数据格式转换器转换为Django API格式
            return DataFormatConverter.pvlib_to_django_format(recent_data)

        except Exception as e:
            self._log_message(f"获取仿真数据失败: {e}")
            return self._get_empty_simulation_data()

    def _get_empty_simulation_data(self) -> Dict[str, List]:
        """返回空的仿真数据结构"""
        return {
            "timestamps": [],
            "ac_power": [],
            "dc_power": [],
            "temp_air": [],
            "temp_cell": [],
            "ghi": [],
            "efficiency": []
        }

    def get_system_info(self) -> Dict[str, Any]:
        """
        获取系统信息（Django API兼容）

        Returns:
            dict: 系统信息字典
        """
        try:
            if not self.pv_twin:
                return self._get_default_system_info()

            # 获取系统容量
            installed_capacity = self.pv_twin.inverter_parameters_dict.get('pdc0', 0) / 1000.0

            # 获取当前功率和其他信息
            current_power = 0
            max_power_today = 0
            max_ghi_today = 0
            max_efficiency_today = 0
            daily_energy = 0
            current_temp_air = 25
            current_temp_cell = 25

            if self.simulation_results is not None and not self.simulation_results.empty:
                latest_data = self.simulation_results.iloc[-1]
                current_power = latest_data.get('ac_power', 0)
                current_temp_air = latest_data.get('temp_air', 25)
                current_temp_cell = latest_data.get('temp_cell', 25)

                # 计算今日最大值
                today_data = self._get_today_data()
                if not today_data.empty:
                    max_power_today = today_data['ac_power'].max()
                    max_ghi_today = today_data.get('ghi', pd.Series([0])).max()

                    # 计算效率
                    efficiency_data = DataFormatConverter._calculate_efficiency(today_data)
                    max_efficiency_today = efficiency_data.max()

                    # 计算日发电量
                    daily_energy = today_data['ac_power'].sum()  # 简化计算

            return {
                "installed_capacity": installed_capacity,
                "current_power": current_power,
                "max_power_today": max_power_today,
                "max_ghi_today": max_ghi_today,
                "max_efficiency_today": max_efficiency_today,
                "daily_energy": daily_energy,
                "current_temp_air": current_temp_air,
                "current_temp_cell": current_temp_cell,
            }

        except Exception as e:
            self._log_message(f"获取系统信息失败: {e}")
            return self._get_default_system_info()

    def _get_default_system_info(self) -> Dict[str, Any]:
        """返回默认系统信息"""
        return {
            "installed_capacity": 5.0,
            "current_power": 0,
            "max_power_today": 0,
            "max_ghi_today": 0,
            "max_efficiency_today": 0,
            "daily_energy": 0,
            "current_temp_air": 25,
            "current_temp_cell": 25,
        }

    def _get_today_data(self) -> pd.DataFrame:
        """获取今日数据"""
        try:
            if self.simulation_results is None or self.simulation_results.empty:
                return pd.DataFrame()

            today = datetime.now().date()

            # 确保有datetime列
            if 'datetime' in self.simulation_results.columns:
                today_mask = self.simulation_results['datetime'].dt.date == today
                return self.simulation_results[today_mask]
            else:
                # 如果没有datetime列，返回最近24小时的数据
                return self.simulation_results.tail(24)

        except Exception as e:
            self._log_message(f"获取今日数据失败: {e}")
            return pd.DataFrame()

    def get_detected_anomalies(self) -> List[Dict]:
        """
        获取检测到的异常（Django API兼容）

        Returns:
            list: 异常列表，格式与原有API兼容
        """
        try:
            if not self.anomaly_model:
                return []

            # 检查缓存
            current_time = time.time()
            if (self._last_anomaly_check_time and
                current_time - self._last_anomaly_check_time < self._anomaly_cache_duration):
                return getattr(self, '_cached_anomalies', [])

            # 获取异常检测结果
            if hasattr(self.anomaly_model, 'get_detected_anomalies_summary'):
                anomalies_summary = self.anomaly_model.get_detected_anomalies_summary()

                # 转换为Django API兼容格式
                anomalies = []
                for anomaly in anomalies_summary:
                    # 确保时间戳格式正确
                    timestamp = anomaly.get('timestamp', '')
                    if hasattr(timestamp, 'strftime'):
                        timestamp_str = timestamp.strftime('%Y-%m-%d %H:%M:%S')
                    else:
                        timestamp_str = str(timestamp)

                    # 构建兼容的异常数据结构
                    anomaly_data = {
                        'timestamp': timestamp_str,
                        'type': anomaly.get('type', 'UNKNOWN'),
                        'severity': anomaly.get('severity', 0.0),
                        'description': self._get_anomaly_description(anomaly.get('type', 'UNKNOWN')),
                        'mae': anomaly.get('mae', 0.0),
                        'applied_effect': anomaly.get('applied_effect')
                    }
                    anomalies.append(anomaly_data)
            else:
                anomalies = []

            # 缓存结果
            self._cached_anomalies = anomalies
            self._last_anomaly_check_time = current_time

            return anomalies

        except Exception as e:
            self._log_message(f"获取异常检测结果失败: {e}")
            return []

    def _get_anomaly_description(self, anomaly_type: str) -> str:
        """
        获取异常类型的描述信息

        Args:
            anomaly_type: 异常类型

        Returns:
            str: 异常描述
        """
        descriptions = {
            'RECONSTRUCTION_FAULT': '重构异常：系统输出与预期模式存在显著偏差',
            'PERFORMANCE_DEGRADATION': '性能退化：系统效率低于正常水平',
            'TEMPERATURE_ANOMALY': '温度异常：组件温度超出正常范围',
            'HOTSPOT': '热点效应：局部过热可能导致性能损失',
            'DEGRADATION': '老化退化：长期使用导致的性能衰减',
            'UNKNOWN': '未知异常：检测到异常但类型不明确'
        }
        return descriptions.get(anomaly_type, f'异常类型: {anomaly_type}')

    def get_daily_energy(self) -> List[Dict]:
        """
        获取每日能量数据（Django API兼容）

        Returns:
            list: 每日能量数据列表
        """
        try:
            if self.simulation_results is None or self.simulation_results.empty:
                return []

            # 按日期分组计算能量
            daily_data = []

            if 'datetime' in self.simulation_results.columns:
                grouped = self.simulation_results.groupby(
                    self.simulation_results['datetime'].dt.date
                )

                for date, group in grouped:
                    daily_energy = group['ac_power'].sum()  # 简化计算
                    daily_data.append({
                        'date': date.strftime('%Y-%m-%d'),
                        'energy': daily_energy
                    })

            return daily_data

        except Exception as e:
            self._log_message(f"获取每日能量数据失败: {e}")
            return []

    def get_simulation_logs(self) -> List[str]:
        """
        获取仿真日志（Django API兼容）

        Returns:
            list: 日志列表
        """
        return RealPVModelAdapter._simulation_logs.copy()

    def apply_settings(self, settings: Dict[str, Any]) -> tuple[bool, str]:
        """
        应用系统设置（Django API兼容）

        Args:
            settings: 设置字典

        Returns:
            tuple: (成功标志, 消息)
        """
        try:
            self._log_message(f"开始应用设置: {settings}")

            # 提取设置参数
            latitude = settings.get('latitude', self.pv_twin.location.latitude)
            longitude = settings.get('longitude', self.pv_twin.location.longitude)
            system_capacity = settings.get('system_capacity', 5.0)  # kW
            temp_coeff = settings.get('temp_coeff', -0.4)  # %/°C
            system_loss = settings.get('system_loss', 2.0)  # %
            modules_per_string = settings.get('modules_per_string', 10)
            strings_per_inverter = settings.get('strings_per_inverter', 2)

            # 计算模块参数
            total_modules = modules_per_string * strings_per_inverter
            module_pdc0 = (system_capacity * 1000) / total_modules  # 单个模块功率

            # 创建新的PV数字孪生模型
            from model.pv_model import PVDigitalTwin

            new_pv_twin = PVDigitalTwin(
                latitude=latitude,
                longitude=longitude,
                altitude=getattr(self.pv_twin.location, 'altitude', 50),
                name="Updated Real PV Digital Twin System",
                module_parameters={
                    "pdc0": module_pdc0,
                    "gamma_pdc": temp_coeff / 100.0,  # 转换为1/°C
                },
                inverter_parameters={
                    "pdc0": system_capacity * 1000  # 系统总功率
                },
                modules_per_string=modules_per_string,
                strings_per_inverter=strings_per_inverter,
                system_loss_dc_ohmic=system_loss / 100.0,  # 转换为小数
                transformer_efficiency=0.985
            )

            # 更新PV模型
            self.pv_twin = new_pv_twin
            self._log_message("PV数字孪生模型已更新")

            # 更新异常检测模型
            if self.anomaly_model:
                self.anomaly_model.pv_digital_twin_model = self.pv_twin
                self._log_message("异常检测模型已更新")

            # 更新仿真引擎
            if self.simulation_engine:
                self.simulation_engine.pv_model = self.pv_twin
                self.simulation_engine.anomaly_model = self.anomaly_model
                self._log_message("仿真引擎已更新")

            # 重新生成天气数据和运行仿真
            self._generate_initial_weather_data()
            self._run_initial_simulation()

            self._log_message("设置应用成功")
            return True, "设置已成功应用"

        except Exception as e:
            error_msg = f"应用设置失败: {str(e)}"
            self._log_message(error_msg)
            return False, error_msg

    def reset_simulation(self) -> tuple[bool, str]:
        """
        重置仿真（Django API兼容）

        Returns:
            tuple: (成功标志, 消息)
        """
        try:
            self._log_message("开始重置仿真")

            # 停止持续仿真
            self._continuous_simulation_active = False
            self._is_running = False

            # 清除仿真结果
            self.simulation_results = pd.DataFrame()

            # 重新初始化
            self._generate_initial_weather_data()
            self._run_initial_simulation()
            self._start_continuous_simulation()

            self._log_message("仿真重置成功")
            return True, "仿真已重置"

        except Exception as e:
            error_msg = f"重置仿真失败: {str(e)}"
            self._log_message(error_msg)
            return False, error_msg

    def pause_simulation(self) -> tuple[bool, str]:
        """
        暂停仿真（Django API兼容）

        Returns:
            tuple: (成功标志, 消息)
        """
        try:
            if not self._is_running:
                return False, "仿真未运行"

            if self._is_paused:
                return False, "仿真已暂停"

            self._is_paused = True
            self._log_message("仿真已暂停")
            return True, "仿真已暂停"

        except Exception as e:
            error_msg = f"暂停仿真失败: {str(e)}"
            self._log_message(error_msg)
            return False, error_msg

    def resume_simulation(self) -> tuple[bool, str]:
        """
        恢复仿真（Django API兼容）

        Returns:
            tuple: (成功标志, 消息)
        """
        try:
            if not self._is_running:
                return False, "仿真未运行"

            if not self._is_paused:
                return False, "仿真未暂停"

            self._is_paused = False
            self._log_message("仿真已恢复")
            return True, "仿真已恢复"

        except Exception as e:
            error_msg = f"恢复仿真失败: {str(e)}"
            self._log_message(error_msg)
            return False, error_msg

    def get_simulation_status(self) -> Dict[str, Any]:
        """
        获取仿真状态（Django API兼容）

        Returns:
            dict: 仿真状态信息
        """
        try:
            return {
                "is_running": self._is_running,
                "is_paused": self._is_paused,
                "continuous_simulation_active": self._continuous_simulation_active,
                "has_pv_twin": self.pv_twin is not None,
                "has_anomaly_model": self.anomaly_model is not None,
                "has_simulation_engine": self.simulation_engine is not None,
                "weather_data_points": len(self.weather_data) if self.weather_data is not None else 0,
                "simulation_results_points": len(self.simulation_results) if self.simulation_results is not None else 0,
                "last_update": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

        except Exception as e:
            self._log_message(f"获取仿真状态失败: {e}")
            return {
                "is_running": False,
                "is_paused": False,
                "continuous_simulation_active": False,
                "has_pv_twin": False,
                "has_anomaly_model": False,
                "has_simulation_engine": False,
                "weather_data_points": 0,
                "simulation_results_points": 0,
                "last_update": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "error": str(e)
            }

    # ==================== 阶段四新增API方法 ====================

    def pause_simulation_advanced(self) -> Dict[str, Any]:
        """
        高级仿真暂停功能（使用仿真控制器）

        Returns:
            dict: 操作结果
        """
        try:
            if self.simulation_controller:
                success = self.simulation_controller.pause_simulation()
                if success:
                    self._is_paused = True
                    self._log_message("使用仿真控制器暂停仿真")
                    return {"success": True, "message": "仿真已暂停", "method": "controller"}
                else:
                    return {"success": False, "message": "仿真控制器暂停失败", "method": "controller"}
            else:
                # 回退到基础暂停功能
                success, message = self.pause_simulation()
                return {"success": success, "message": message, "method": "basic"}

        except Exception as e:
            error_msg = f"高级暂停功能失败: {e}"
            self._log_message(error_msg)
            return {"success": False, "message": error_msg, "method": "error"}

    def resume_simulation_advanced(self) -> Dict[str, Any]:
        """
        高级仿真恢复功能（使用仿真控制器）

        Returns:
            dict: 操作结果
        """
        try:
            if self.simulation_controller:
                success = self.simulation_controller.resume_simulation()
                if success:
                    self._is_paused = False
                    self._log_message("使用仿真控制器恢复仿真")
                    return {"success": True, "message": "仿真已恢复", "method": "controller"}
                else:
                    return {"success": False, "message": "仿真控制器恢复失败", "method": "controller"}
            else:
                # 回退到基础恢复功能
                success, message = self.resume_simulation()
                return {"success": success, "message": message, "method": "basic"}

        except Exception as e:
            error_msg = f"高级恢复功能失败: {e}"
            self._log_message(error_msg)
            return {"success": False, "message": error_msg, "method": "error"}

    def update_simulation_parameters(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        动态更新仿真参数

        Args:
            params: 要更新的参数字典

        Returns:
            dict: 更新结果
        """
        try:
            if self.simulation_controller:
                success = self.simulation_controller.update_parameters(params)
                if success:
                    self._log_message(f"仿真参数已更新: {list(params.keys())}")
                    return {"success": True, "message": "参数更新成功", "updated_params": list(params.keys())}
                else:
                    return {"success": False, "message": "仿真控制器参数更新失败"}
            else:
                return {"success": False, "message": "仿真控制器未初始化"}

        except Exception as e:
            error_msg = f"参数更新失败: {e}"
            self._log_message(error_msg)
            return {"success": False, "message": error_msg}

    def change_time_scale(self, time_scale: str) -> Dict[str, Any]:
        """
        切换仿真时间尺度

        Args:
            time_scale: 新的时间尺度 ('secondly', 'minutely', 'hourly', 'daily')

        Returns:
            dict: 切换结果
        """
        try:
            if self.simulation_controller:
                success = self.simulation_controller.change_time_scale(time_scale)
                if success:
                    self._log_message(f"时间尺度已切换到: {time_scale}")
                    return {"success": True, "message": f"时间尺度已切换到: {time_scale}", "time_scale": time_scale}
                else:
                    return {"success": False, "message": "时间尺度切换失败"}
            else:
                return {"success": False, "message": "仿真控制器未初始化"}

        except Exception as e:
            error_msg = f"时间尺度切换失败: {e}"
            self._log_message(error_msg)
            return {"success": False, "message": error_msg}

    def get_advanced_simulation_status(self) -> Dict[str, Any]:
        """
        获取高级仿真状态信息

        Returns:
            dict: 详细的仿真状态信息
        """
        try:
            # 基础状态
            status = self.get_simulation_status()

            # 添加阶段四增强信息
            status.update({
                "phase4_features": {
                    "simulation_controller": self.simulation_controller is not None,
                    "data_persistence": self.data_persistence is not None,
                    "advanced_control_enabled": True
                }
            })

            # 仿真控制器状态
            if self.simulation_controller:
                controller_status = self.simulation_controller.get_simulation_status()
                status["controller_status"] = controller_status

            # 数据持久化统计
            if self.data_persistence:
                persistence_stats = self.data_persistence.get_storage_stats()
                status["persistence_stats"] = persistence_stats

            return status

        except Exception as e:
            self._log_message(f"获取高级仿真状态失败: {e}")
            return {"error": str(e)}

    def query_historical_data(self, start_time: Optional[str] = None,
                            end_time: Optional[str] = None,
                            data_type: Optional[str] = None,
                            limit: int = 100) -> Dict[str, Any]:
        """
        查询历史仿真数据

        Args:
            start_time: 开始时间（ISO格式字符串）
            end_time: 结束时间（ISO格式字符串）
            data_type: 数据类型过滤
            limit: 最大返回条目数

        Returns:
            dict: 查询结果
        """
        try:
            if not self.data_persistence:
                return {"success": False, "message": "数据持久化未启用", "data": []}

            # 转换时间字符串
            start_dt = datetime.fromisoformat(start_time) if start_time else None
            end_dt = datetime.fromisoformat(end_time) if end_time else None

            # 查询数据
            historical_data = self.data_persistence.query_historical_data(
                start_time=start_dt,
                end_time=end_dt,
                data_type=data_type,
                limit=limit
            )

            # 转换为JSON兼容格式
            formatted_data = []
            for item in historical_data:
                formatted_item = {
                    "timestamp": item["timestamp"].isoformat(),
                    "simulation_time": item["simulation_time"],
                    "data_type": item["data_type"],
                    "data": item["data"]
                }
                formatted_data.append(formatted_item)

            return {
                "success": True,
                "message": f"查询到 {len(formatted_data)} 条历史数据",
                "data": formatted_data,
                "query_params": {
                    "start_time": start_time,
                    "end_time": end_time,
                    "data_type": data_type,
                    "limit": limit
                }
            }

        except Exception as e:
            error_msg = f"历史数据查询失败: {e}"
            self._log_message(error_msg)
            return {"success": False, "message": error_msg, "data": []}

    def cleanup_old_data(self) -> Dict[str, Any]:
        """
        清理过期数据

        Returns:
            dict: 清理结果
        """
        try:
            if not self.data_persistence:
                return {"success": False, "message": "数据持久化未启用"}

            success = self.data_persistence.cleanup_old_data()
            if success:
                self._log_message("过期数据清理完成")
                return {"success": True, "message": "过期数据清理完成"}
            else:
                return {"success": False, "message": "数据清理失败"}

        except Exception as e:
            error_msg = f"数据清理失败: {e}"
            self._log_message(error_msg)
            return {"success": False, "message": error_msg}
