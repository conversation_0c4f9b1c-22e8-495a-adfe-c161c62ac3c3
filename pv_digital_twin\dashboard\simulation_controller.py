"""
仿真控制器模块

提供仿真引擎的高级控制功能，包括暂停/恢复、参数动态调整、多时间尺度仿真等。
"""

import threading
import time
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from .config import SimulationConfig

logger = logging.getLogger(__name__)


class SimulationController:
    """
    仿真控制器类
    
    负责管理仿真引擎的运行状态、参数调整和多时间尺度仿真控制。
    """
    
    def __init__(self, simulation_engine=None):
        """
        初始化仿真控制器
        
        Args:
            simulation_engine: 仿真引擎实例
        """
        self.simulation_engine = simulation_engine
        self.config = SimulationConfig.get_simulation_config()
        self.control_config = self.config.get('SIMULATION_CONTROL', {})
        
        # 控制状态
        self.is_running = False
        self.is_paused = False
        self.current_time_scale = self.control_config.get('MULTI_TIME_SCALE', {}).get('DEFAULT_SCALE', 'hourly')
        
        # 参数缓存
        self.dynamic_params = {}
        self.last_save_time = time.time()
        
        # 线程控制
        self.control_thread = None
        self.stop_event = threading.Event()
        
        logger.info("仿真控制器初始化完成")
    
    def set_simulation_engine(self, simulation_engine):
        """设置仿真引擎"""
        self.simulation_engine = simulation_engine
        logger.info("仿真引擎已设置")
    
    def start_simulation(self, **kwargs) -> bool:
        """
        启动仿真
        
        Args:
            **kwargs: 仿真参数
            
        Returns:
            bool: 启动是否成功
        """
        if not self.simulation_engine:
            logger.error("仿真引擎未设置")
            return False
        
        if self.is_running:
            logger.warning("仿真已在运行中")
            return False
        
        try:
            # 配置仿真参数
            if kwargs:
                self.simulation_engine.configure_simulation(**kwargs)
            
            # 启动仿真引擎
            self.simulation_engine.start()
            self.is_running = True
            self.is_paused = False
            
            # 启动控制线程
            if self.control_config.get('ENABLE_PAUSE_RESUME', False):
                self._start_control_thread()
            
            logger.info("仿真启动成功")
            return True
            
        except Exception as e:
            logger.error(f"仿真启动失败: {e}")
            return False
    
    def pause_simulation(self) -> bool:
        """
        暂停仿真
        
        Returns:
            bool: 暂停是否成功
        """
        if not self.control_config.get('ENABLE_PAUSE_RESUME', False):
            logger.warning("暂停/恢复功能未启用")
            return False
        
        if not self.is_running or self.is_paused:
            logger.warning("仿真未运行或已暂停")
            return False
        
        try:
            if hasattr(self.simulation_engine, 'pause'):
                self.simulation_engine.pause()
            self.is_paused = True
            logger.info("仿真已暂停")
            return True
            
        except Exception as e:
            logger.error(f"暂停仿真失败: {e}")
            return False
    
    def resume_simulation(self) -> bool:
        """
        恢复仿真
        
        Returns:
            bool: 恢复是否成功
        """
        if not self.control_config.get('ENABLE_PAUSE_RESUME', False):
            logger.warning("暂停/恢复功能未启用")
            return False
        
        if not self.is_running or not self.is_paused:
            logger.warning("仿真未运行或未暂停")
            return False
        
        try:
            if hasattr(self.simulation_engine, 'resume'):
                self.simulation_engine.resume()
            self.is_paused = False
            logger.info("仿真已恢复")
            return True
            
        except Exception as e:
            logger.error(f"恢复仿真失败: {e}")
            return False
    
    def stop_simulation(self) -> bool:
        """
        停止仿真
        
        Returns:
            bool: 停止是否成功
        """
        try:
            if hasattr(self.simulation_engine, 'stop'):
                self.simulation_engine.stop()
            
            self.is_running = False
            self.is_paused = False
            
            # 停止控制线程
            self._stop_control_thread()
            
            logger.info("仿真已停止")
            return True
            
        except Exception as e:
            logger.error(f"停止仿真失败: {e}")
            return False
    
    def update_parameters(self, params: Dict[str, Any]) -> bool:
        """
        动态更新仿真参数
        
        Args:
            params: 要更新的参数字典
            
        Returns:
            bool: 更新是否成功
        """
        if not self.control_config.get('ENABLE_DYNAMIC_PARAMS', False):
            logger.warning("动态参数调整功能未启用")
            return False
        
        try:
            # 缓存参数
            self.dynamic_params.update(params)
            
            # 应用参数到仿真引擎
            if hasattr(self.simulation_engine, 'update_parameters'):
                self.simulation_engine.update_parameters(params)
            
            logger.info(f"参数更新成功: {list(params.keys())}")
            return True
            
        except Exception as e:
            logger.error(f"参数更新失败: {e}")
            return False
    
    def change_time_scale(self, time_scale: str) -> bool:
        """
        切换时间尺度
        
        Args:
            time_scale: 新的时间尺度 ('secondly', 'minutely', 'hourly', 'daily')
            
        Returns:
            bool: 切换是否成功
        """
        multi_time_config = self.control_config.get('MULTI_TIME_SCALE', {})
        if not multi_time_config.get('ENABLE', False):
            logger.warning("多时间尺度功能未启用")
            return False
        
        available_scales = multi_time_config.get('AVAILABLE_SCALES', [])
        if time_scale not in available_scales:
            logger.error(f"不支持的时间尺度: {time_scale}")
            return False
        
        try:
            # 更新时间尺度配置
            if hasattr(self.simulation_engine, 'change_time_scale'):
                self.simulation_engine.change_time_scale(time_scale)
            
            self.current_time_scale = time_scale
            logger.info(f"时间尺度已切换到: {time_scale}")
            return True
            
        except Exception as e:
            logger.error(f"时间尺度切换失败: {e}")
            return False
    
    def get_simulation_status(self) -> Dict[str, Any]:
        """
        获取仿真状态
        
        Returns:
            dict: 仿真状态信息
        """
        status = {
            'is_running': self.is_running,
            'is_paused': self.is_paused,
            'current_time_scale': self.current_time_scale,
            'dynamic_params': self.dynamic_params.copy(),
            'last_save_time': self.last_save_time,
            'control_features': {
                'pause_resume_enabled': self.control_config.get('ENABLE_PAUSE_RESUME', False),
                'dynamic_params_enabled': self.control_config.get('ENABLE_DYNAMIC_PARAMS', False),
                'multi_time_scale_enabled': self.control_config.get('MULTI_TIME_SCALE', {}).get('ENABLE', False)
            }
        }
        
        # 添加仿真引擎状态
        if self.simulation_engine and hasattr(self.simulation_engine, 'get_status'):
            status['engine_status'] = self.simulation_engine.get_status()
        
        return status
    
    def _start_control_thread(self):
        """启动控制线程"""
        if self.control_thread and self.control_thread.is_alive():
            return
        
        self.stop_event.clear()
        self.control_thread = threading.Thread(target=self._control_loop, daemon=True)
        self.control_thread.start()
        logger.info("控制线程已启动")
    
    def _stop_control_thread(self):
        """停止控制线程"""
        if self.control_thread and self.control_thread.is_alive():
            self.stop_event.set()
            self.control_thread.join(timeout=5)
            logger.info("控制线程已停止")
    
    def _control_loop(self):
        """控制循环"""
        auto_save_interval = self.control_config.get('AUTO_SAVE_INTERVAL', 3600)
        
        while not self.stop_event.is_set():
            try:
                # 检查自动保存
                current_time = time.time()
                if current_time - self.last_save_time >= auto_save_interval:
                    self._auto_save()
                    self.last_save_time = current_time
                
                # 检查内存使用
                self._check_memory_usage()
                
                # 等待一段时间
                self.stop_event.wait(60)  # 每分钟检查一次
                
            except Exception as e:
                logger.error(f"控制循环错误: {e}")
                time.sleep(60)
    
    def _auto_save(self):
        """自动保存"""
        try:
            if hasattr(self.simulation_engine, 'save_state'):
                self.simulation_engine.save_state()
            logger.debug("自动保存完成")
        except Exception as e:
            logger.error(f"自动保存失败: {e}")
    
    def _check_memory_usage(self):
        """检查内存使用"""
        try:
            import psutil
            import os
            
            process = psutil.Process(os.getpid())
            memory_mb = process.memory_info().rss / 1024 / 1024
            max_memory = self.control_config.get('MAX_MEMORY_USAGE_MB', 512)
            
            if memory_mb > max_memory:
                logger.warning(f"内存使用过高: {memory_mb:.1f}MB > {max_memory}MB")
                # 可以在这里实现内存清理逻辑
                
        except ImportError:
            # psutil未安装，跳过内存检查
            pass
        except Exception as e:
            logger.error(f"内存检查失败: {e}")
