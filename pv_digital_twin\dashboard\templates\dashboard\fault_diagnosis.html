{% extends 'base.html' %}

{% block title %}故障诊断 - 光伏数字孪生平台{% endblock %}

{% block content %}
<div class="row mb-3">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center">
            <h2>故障诊断</h2>
            <div>
                <div class="form-check form-switch d-inline-block me-3">
                    <input class="form-check-input" type="checkbox" id="real-time-update" checked>
                    <label class="form-check-label" for="real-time-update">实时更新</label>
                </div>
                <button id="manual-refresh" class="btn btn-primary btn-sm">
                    <i class="bi bi-arrow-clockwise"></i> 手动刷新
                </button>
                <div id="update-status" class="text-muted small mt-1">
                    <span id="last-update-time">正在加载...</span>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header">故障诊断结果</div>
            <div class="card-body">
                <h5>检测到的异常:</h5>
                <ul id="fault-causes-list">
                    {% for cause in fault_causes %}
                    <li>{{ cause.type }} - 严重程度: {{ cause.severity|floatformat:2 }} ({{ cause.timestamp }})</li>
                    {% empty %}
                    <li>目前无自动检测到的异常</li>
                    {% endfor %}
                </ul>
                <h5>建议措施:</h5>
                <ul id="recommendations-list">
                    <li>请检查下方图表分析具体情况。</li>
                    <li>对于持续性异常，考虑安排现场检查。</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- 故障注入设置表单 -->
<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header">为下一天仿真设置计划故障</div>
            <div class="card-body">
                <form id="schedule-fault-form">
                    {% csrf_token %}
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="fault-type" class="form-label">故障类型</label>
                            <select class="form-select" id="fault-type" name="fault_type">
                                <option value="PARTIAL_SHADING">部分阴影遮挡</option>
                                <option value="HOTSPOT">组件热斑</option>
                                <option value="AGING_DEGRADATION">组件老化衰减</option>
                                <option value="INVERTER_EFFICIENCY_DROP">逆变器效率下降</option>
                                <!-- 更多故障类型可以从后端动态加载 -->
                            </select>
                        </div>
                        <div class="col-md-2 mb-3">
                            <label for="fault-start-hour" class="form-label">开始时间 (小时)</label>
                            <input type="number" class="form-control" id="fault-start-hour" name="start_hour" min="0"
                                max="23" value="8">
                        </div>
                        <div class="col-md-2 mb-3">
                            <label for="fault-end-hour" class="form-label">结束时间 (小时)</label>
                            <input type="number" class="form-control" id="fault-end-hour" name="end_hour" min="0"
                                max="23" value="10">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="fault-severity" class="form-label">严重程度 (0.1-1.0)</label>
                            <input type="number" class="form-control" id="fault-severity" name="severity" step="0.05"
                                min="0.05" max="1.0" value="0.5">
                        </div>
                        <div class="col-md-2 mb-3 d-flex align-items-end">
                            <button type="button" class="btn btn-info w-100" id="add-fault-to-list">添加到列表</button>
                        </div>
                    </div>
                </form>

                <h5 class="mt-4">计划的故障列表 (将应用于下一天的仿真):</h5>
                <ul id="scheduled-faults-list" class="list-group mb-3">
                    <!-- 计划的故障将在这里显示 -->
                </ul>
                <button type="button" class="btn btn-primary" id="apply-scheduled-faults">应用计划故障到下一天</button>
                <div id="schedule-feedback" class="mt-2"></div>
            </div>
        </div>
    </div>
</div>
<!-- END 故障注入设置表单 -->

<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header">功率异常检测</div>
            <div class="card-body">
                <div id="anomaly-chart" style="height: 400px;"></div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-5">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">预测误差分析</div>
            <div class="card-body">
                <div id="error-chart" style="height: 350px;"></div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">实际功率与预测功率对比</div>
            <div class="card-body">
                <div id="comparison-chart" style="height: 350px;"></div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 定义全局变量
    let anomalyChart, errorChart, comparisonChart;
    let updateTimer; // 实时更新定时器
    let isRealTimeUpdateEnabled = true; // 实时更新开关

    document.addEventListener('DOMContentLoaded', function () {
        // 初始化图表等
        loadAnomalyData();

        // 启动实时更新
        startRealTimeUpdates();

        // 实时更新开关事件处理
        const realTimeUpdateSwitch = document.getElementById('real-time-update');
        if (realTimeUpdateSwitch) {
            realTimeUpdateSwitch.addEventListener('change', function () {
                isRealTimeUpdateEnabled = this.checked;
                if (isRealTimeUpdateEnabled) {
                    console.log('故障诊断实时更新已启用');
                    // 立即更新一次数据
                    loadAnomalyData();
                } else {
                    console.log('故障诊断实时更新已暂停');
                }
                updateLastUpdateTime();
            });
        }

        // 手动刷新按钮事件处理
        const manualRefreshButton = document.getElementById('manual-refresh');
        if (manualRefreshButton) {
            manualRefreshButton.addEventListener('click', function () {
                loadAnomalyData();
                console.log('手动刷新故障诊断数据');
            });
        }

        const addFaultButton = document.getElementById('add-fault-to-list');
        const applyScheduledFaultsButton = document.getElementById('apply-scheduled-faults');
        const scheduledFaultsList = document.getElementById('scheduled-faults-list');
        const scheduleFeedback = document.getElementById('schedule-feedback');

        let plannedFaults = []; // 用于存储在客户端添加的故障

        addFaultButton.addEventListener('click', function () {
            const faultTypeSelect = document.getElementById('fault-type');
            const faultTypeValue = faultTypeSelect.value;
            const faultTypeText = faultTypeSelect.options[faultTypeSelect.selectedIndex].text;
            const startHour = document.getElementById('fault-start-hour').value;
            const endHour = document.getElementById('fault-end-hour').value;
            const severity = document.getElementById('fault-severity').value;

            if (!faultTypeValue || startHour === '' || endHour === '' || severity === '') {
                alert('请填写所有故障参数！');
                return;
            }
            if (parseInt(startHour) >= parseInt(endHour)) {
                alert('开始时间必须小于结束时间！');
                return;
            }
            if (parseFloat(severity) < 0.05 || parseFloat(severity) > 1.0) {
                alert('严重程度必须在 0.05 和 1.0 之间！');
                return;
            }

            const fault = {
                type: faultTypeValue,
                type_display: faultTypeText,
                start_hour: parseInt(startHour),
                end_hour: parseInt(endHour),
                severity: parseFloat(severity)
            };
            plannedFaults.push(fault);
            renderScheduledFaults();
        });

        function renderScheduledFaults() {
            scheduledFaultsList.innerHTML = ''; // 清空列表
            if (plannedFaults.length === 0) {
                const listItem = document.createElement('li');
                listItem.classList.add('list-group-item');
                listItem.textContent = '暂无计划的故障';
                scheduledFaultsList.appendChild(listItem);
            } else {
                plannedFaults.forEach((fault, index) => {
                    const listItem = document.createElement('li');
                    listItem.classList.add('list-group-item', 'd-flex', 'justify-content-between', 'align-items-center');
                    listItem.innerHTML = `
                        <span><strong>${fault.type_display}</strong>: ${fault.start_hour}:00 - ${fault.end_hour}:00, 严重性: ${fault.severity.toFixed(2)}</span>
                        <button type="button" class="btn btn-sm btn-danger remove-fault-btn" data-index="${index}">移除</button>
                    `;
                    scheduledFaultsList.appendChild(listItem);
                });
                document.querySelectorAll('.remove-fault-btn').forEach(button => {
                    button.addEventListener('click', function () {
                        const indexToRemove = parseInt(this.getAttribute('data-index'));
                        plannedFaults.splice(indexToRemove, 1);
                        renderScheduledFaults();
                    });
                });
            }
        }

        applyScheduledFaultsButton.addEventListener('click', function () {
            if (plannedFaults.length === 0) {
                scheduleFeedback.innerHTML = '<div class="alert alert-warning">没有计划的故障可以应用。</div>';
                return;
            }

            const csrfTokenElement = document.querySelector('[name=csrfmiddlewaretoken]');
            if (!csrfTokenElement) {
                console.error('CSRF token not found!');
                scheduleFeedback.innerHTML = '<div class="alert alert-danger">内部错误: CSRF token缺失。</div>';
                return;
            }
            const csrfToken = csrfTokenElement.value;

            fetch('/dashboard/schedule-faults/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                },
                body: JSON.stringify({ faults: plannedFaults })
            })
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(err => { throw new Error(err.message || '请求失败') });
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.status === 'success') {
                        scheduleFeedback.innerHTML = `<div class="alert alert-success">${data.message}</div>`;
                        plannedFaults = [];
                        renderScheduledFaults();
                    } else {
                        scheduleFeedback.innerHTML = `<div class="alert alert-danger">应用故障失败: ${data.message}</div>`;
                    }
                })
                .catch(error => {
                    scheduleFeedback.innerHTML = `<div class="alert alert-danger">请求失败: ${error.message}</div>`;
                    console.error('Error scheduling faults:', error);
                });
        });

        renderScheduledFaults(); // 初始化列表显示
    });

    function updateFaultCauses(anomalyData) {
        const faultCausesList = document.getElementById('fault-causes-list');
        faultCausesList.innerHTML = '';

        if (anomalyData && anomalyData.length > 0) {
            anomalyData.forEach(anomaly => {
                const listItem = document.createElement('li');
                listItem.textContent = `${anomaly.type} - 严重程度: ${anomaly.severity.toFixed(2)} (发生于: ${anomaly.timestamp})`;
                faultCausesList.appendChild(listItem);
            });
        } else {
            const listItem = document.createElement('li');
            listItem.textContent = '目前无自动检测到的异常';
            faultCausesList.appendChild(listItem);
        }
    }

    // 更新最后更新时间显示
    function updateLastUpdateTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('zh-CN');
        const statusElement = document.getElementById('last-update-time');
        if (statusElement) {
            if (isRealTimeUpdateEnabled) {
                statusElement.textContent = `最后更新: ${timeString} (实时更新中)`;
            } else {
                statusElement.textContent = `最后更新: ${timeString} (已暂停)`;
            }
        }
    }

    function loadAnomalyData() {
        fetch('/api/simulation-data/')
            .then(response => response.json())
            .then(simData => {
                fetch('/api/anomaly-data/')
                    .then(response => response.json())
                    .then(anomalyApiResponse => {
                        const anomalyData = anomalyApiResponse.detected_anomalies || anomalyApiResponse; // 兼容旧格式
                        updateFaultCauses(anomalyData);

                        const timestamps = simData.timestamps || [];
                        const acPower = simData.ac_power || [];
                        const ghi = simData.ghi || [];

                        const autoDetectedAnomalyPoints = [];
                        const autoDetectedAnomalyValues = [];
                        const autoDetectedAnomalySeverity = [];

                        (anomalyData).forEach(anomaly => {
                            const timestamp = anomaly.timestamp;
                            const idx = timestamps.findIndex(ts => {
                                if (!ts || !timestamp) return false;
                                const d1 = new Date(ts.substring(0, 16)); // 比较到分钟
                                const d2 = new Date(timestamp.substring(0, 16));
                                return d1.getTime() === d2.getTime();
                            });
                            if (idx !== -1) {
                                autoDetectedAnomalyPoints.push(idx);
                                autoDetectedAnomalyValues.push(acPower[idx]);
                                autoDetectedAnomalySeverity.push(anomaly.severity);
                            }
                        });

                        const seriesDataAcPower = acPower.map(p => p != null ? p / 1000 : null);
                        const seriesDataGhi = ghi.map(g => g != null ? g : null);
                        const seriesDataAnomaly = acPower.map((value, index) => {
                            return autoDetectedAnomalyPoints.includes(index) && value != null ? value / 1000 : '-';
                        });

                        if (anomalyChart) {
                            // 使用notMerge: false来优化性能，只更新数据部分
                            anomalyChart.setOption({
                                xAxis: { data: timestamps },
                                series: [
                                    { name: '交流功率', data: seriesDataAcPower },
                                    { name: '辐照度', data: seriesDataGhi },
                                    { name: '自动检测异常', data: seriesDataAnomaly }
                                ]
                            }, false); // notMerge: false 提高性能
                        } else {
                            anomalyChart = echarts.init(document.getElementById('anomaly-chart'));
                            const anomalyOption = {
                                title: { text: '功率异常检测' },
                                tooltip: {
                                    trigger: 'axis',
                                    axisPointer: { type: 'cross' },
                                    formatter: function (params) {
                                        let result = params[0].name + '<br/>';
                                        params.forEach(param => {
                                            if (param.seriesName === '自动检测异常') {
                                                const idx = autoDetectedAnomalyPoints.indexOf(param.dataIndex);
                                                if (idx !== -1 && param.value !== '-') {
                                                    result += `${param.seriesName}: ${parseFloat(param.value).toFixed(2)} kW<br/>`;
                                                    result += `严重程度: ${autoDetectedAnomalySeverity[idx].toFixed(2)}<br/>`;
                                                }
                                            } else if (param.value != null && param.value !== '-') {
                                                result += `${param.seriesName}: ${parseFloat(param.value).toFixed(2)} ${param.seriesName === '辐照度' ? 'W/m²' : 'kW'}<br/>`;
                                            }
                                        });
                                        return result;
                                    }
                                },
                                legend: { data: ['交流功率', '辐照度', '自动检测异常'] },
                                xAxis: { type: 'category', data: timestamps },
                                yAxis: [
                                    { type: 'value', name: '功率 (kW)', position: 'left', min: 0, axisLabel: { formatter: '{value} kW' } },
                                    { type: 'value', name: '辐照度 (W/m²)', position: 'right', min: 0, axisLabel: { formatter: '{value} W/m²' } }
                                ],
                                series: [
                                    { name: '交流功率', type: 'line', data: seriesDataAcPower, smooth: true },
                                    { name: '辐照度', type: 'line', yAxisIndex: 1, data: seriesDataGhi, smooth: true },
                                    {
                                        name: '自动检测异常',
                                        type: 'scatter',
                                        symbolSize: function (val, params) {
                                            const idx = autoDetectedAnomalyPoints.indexOf(params.dataIndex);
                                            return idx !== -1 && val !== '-' ? 10 + autoDetectedAnomalySeverity[idx] * 10 : 0;
                                        },
                                        data: seriesDataAnomaly,
                                        itemStyle: { color: '#c23531' }
                                    }
                                ]
                            };
                            anomalyChart.setOption(anomalyOption);
                        }

                        const recentPoints = Math.min(timestamps.length, 48);
                        const recentTimestamps = timestamps.slice(-recentPoints);
                        const recentAcPower = seriesDataAcPower.slice(-recentPoints);
                        const predictedPower = acPower.map(p => p != null ? p * (0.9 + Math.random() * 0.2) : null);
                        const recentPredictedPower = predictedPower.map(p => p != null ? p / 1000 : null).slice(-recentPoints);
                        const errors = acPower.map((actual, idx) => actual != null && predictedPower[idx] != null ? (actual - predictedPower[idx]) / 1000 : null);
                        const recentErrors = errors.slice(-recentPoints);

                        if (errorChart) {
                            errorChart.setOption({
                                xAxis: { data: recentTimestamps },
                                series: [{ name: '预测误差', data: recentErrors }]
                            }, false); // 优化性能
                        } else {
                            errorChart = echarts.init(document.getElementById('error-chart'));
                            const errorOption = {
                                title: { text: `预测误差分析 (最近${recentPoints}点)` },
                                tooltip: { trigger: 'axis' },
                                xAxis: { type: 'category', data: recentTimestamps },
                                yAxis: { type: 'value', name: '误差 (kW)', axisLabel: { formatter: '{value} kW' } },
                                series: [{
                                    name: '预测误差', type: 'bar', data: recentErrors,
                                    itemStyle: { color: function (params) { return params.value > 0 ? '#c23531' : '#3398DB'; } }
                                }]
                            };
                            errorChart.setOption(errorOption);
                        }

                        if (comparisonChart) {
                            comparisonChart.setOption({
                                xAxis: { data: recentTimestamps },
                                series: [
                                    { name: '实际功率', data: recentAcPower },
                                    { name: '预测功率', data: recentPredictedPower }
                                ]
                            }, false); // 优化性能
                        } else {
                            comparisonChart = echarts.init(document.getElementById('comparison-chart'));
                            const comparisonOption = {
                                title: { text: `实际与预测功率对比 (最近${recentPoints}点)` },
                                tooltip: { trigger: 'axis' },
                                legend: { data: ['实际功率', '预测功率'] },
                                xAxis: { type: 'category', data: recentTimestamps },
                                yAxis: { type: 'value', name: '功率 (kW)', axisLabel: { formatter: '{value} kW' } },
                                series: [
                                    { name: '实际功率', type: 'line', data: recentAcPower, smooth: true },
                                    { name: '预测功率', type: 'line', data: recentPredictedPower, smooth: true }
                                ]
                            };
                            comparisonChart.setOption(comparisonOption);
                        }

                        // 更新最后更新时间
                        updateLastUpdateTime();
                    })
                    .catch(error => {
                        console.error('Error loading anomaly data:', error);
                        updateLastUpdateTime();
                    });
            })
            .catch(error => {
                console.error('Error loading simulation data:', error);
                updateLastUpdateTime();
            });
    }

    // 启动实时数据更新
    function startRealTimeUpdates() {
        // 立即更新一次
        loadAnomalyData();

        // 设置定时更新 - 每5秒更新一次，与主仪表盘保持一致
        updateTimer = setInterval(() => {
            if (isRealTimeUpdateEnabled) {
                loadAnomalyData();
            }
        }, 5000);
    }

    // 停止实时数据更新
    function stopRealTimeUpdates() {
        if (updateTimer) {
            clearInterval(updateTimer);
            updateTimer = null;
        }
    }

    // 切换实时更新状态
    function toggleRealTimeUpdate() {
        isRealTimeUpdateEnabled = !isRealTimeUpdateEnabled;
        if (isRealTimeUpdateEnabled) {
            console.log('故障诊断实时更新已启用');
        } else {
            console.log('故障诊断实时更新已暂停');
        }
    }

    // 页面卸载时清理定时器
    window.addEventListener('beforeunload', function () {
        stopRealTimeUpdates();
    });

    // 页面可见性变化时的处理
    document.addEventListener('visibilitychange', function () {
        if (document.hidden) {
            // 页面隐藏时暂停更新以节省资源
            isRealTimeUpdateEnabled = false;
        } else {
            // 页面重新可见时恢复更新
            isRealTimeUpdateEnabled = true;
            // 立即更新一次数据
            loadAnomalyData();
        }
    });

    // 窗口大小变化时重新调整图表大小
    window.addEventListener('resize', function () {
        if (anomalyChart) anomalyChart.resize();
        if (errorChart) errorChart.resize();
        if (comparisonChart) comparisonChart.resize();
    });

</script>
{% endblock %}