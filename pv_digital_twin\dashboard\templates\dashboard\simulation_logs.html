{% extends 'base.html' %}

{% block title %}仿真日志 - 光伏数字孪生平台{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <h2 class="text-center">仿真日志</h2>
            <div class="text-end">
                <div class="form-check form-switch d-inline-block me-3">
                    <input class="form-check-input" type="checkbox" id="auto-refresh" checked>
                    <label class="form-check-label" for="auto-refresh">自动刷新</label>
                </div>
                <button id="refresh-logs" class="btn btn-primary btn-sm">
                    <i class="bi bi-arrow-clockwise"></i> 刷新日志
                </button>
                <button id="clear-logs" class="btn btn-outline-danger btn-sm ms-2">
                    <i class="bi bi-trash"></i> 清空日志
                </button>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>系统仿真日志</div>
                    <div class="text-muted" id="log-count">共 <span id="log-entries-count">{{ logs|length }}</span> 条日志</div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th width="25%">时间戳</th>
                                    <th width="75%">消息</th>
                                </tr>
                            </thead>
                            <tbody id="logs-table-body">
                                {% if logs %}
                                    {% for log in logs %}
                                    <tr>
                                        <td>{{ log.timestamp }}</td>
                                        <td>{{ log.message }}</td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr id="no-logs-row">
                                        <td colspan="2" class="text-center">暂无日志记录</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
        const logsTableBody = document.getElementById('logs-table-body');
        const refreshLogsBtn = document.getElementById('refresh-logs');
        const clearLogsBtn = document.getElementById('clear-logs');
        const autoRefreshCheckbox = document.getElementById('auto-refresh');
        const logEntriesCountSpan = document.getElementById('log-entries-count');
        
        let autoRefreshInterval;
        
        // 刷新日志函数
        function refreshLogs() {
            fetch('/api/simulation-logs/')
                .then(response => response.json())
                .then(data => {
                    const logs = data.logs || [];
                    
                    // 更新日志条数
                    logEntriesCountSpan.textContent = logs.length;
                    
                    // 清空表格
                    logsTableBody.innerHTML = '';
                    
                    if (logs.length === 0) {
                        // 如果没有日志，显示提示
                        const noLogsRow = document.createElement('tr');
                        noLogsRow.id = 'no-logs-row';
                        noLogsRow.innerHTML = '<td colspan="2" class="text-center">暂无日志记录</td>';
                        logsTableBody.appendChild(noLogsRow);
                    } else {
                        // 添加日志行
                        logs.forEach(log => {
                            const row = document.createElement('tr');
                            row.innerHTML = `
                                <td>${log.timestamp}</td>
                                <td>${log.message}</td>
                            `;
                            logsTableBody.appendChild(row);
                        });
                    }
                })
                .catch(error => {
                    console.error('获取日志失败:', error);
                });
        }
        
        // 开始自动刷新
        function startAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
            autoRefreshInterval = setInterval(refreshLogs, 5000); // 每5秒刷新一次
        }
        
        // 停止自动刷新
        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
            }
        }
        
        // 刷新按钮点击事件
        refreshLogsBtn.addEventListener('click', refreshLogs);
        
        // 清空日志按钮点击事件
        clearLogsBtn.addEventListener('click', function() {
            if (confirm('确定要清空日志吗？此操作不可撤销。')) {
                // 重置仿真以清空日志
                fetch('/api/reset-simulation/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        refreshLogs();
                    } else {
                        alert('清空日志失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('清空日志失败:', error);
                    alert('清空日志失败，请查看控制台了解详情。');
                });
            }
        });
        
        // 自动刷新切换事件
        autoRefreshCheckbox.addEventListener('change', function() {
            if (this.checked) {
                startAutoRefresh();
            } else {
                stopAutoRefresh();
            }
        });
        
        // 初始化自动刷新
        if (autoRefreshCheckbox.checked) {
            startAutoRefresh();
        }
        
        // 页面卸载时清理
        window.addEventListener('beforeunload', function() {
            stopAutoRefresh();
        });
    });
</script>
{% endblock %} 