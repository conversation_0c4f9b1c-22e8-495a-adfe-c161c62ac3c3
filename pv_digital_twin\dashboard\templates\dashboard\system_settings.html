{% extends 'base.html' %}

{% block title %}系统设置 - 光伏数字孪生平台{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">系统参数设置</div>
            <div class="card-body">
                <form id="system-settings-form">
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label class="form-label">系统位置</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <input type="number" class="form-control" id="latitude" name="latitude" placeholder="纬度" value="{{ latitude }}" step="0.0001">
                                </div>
                                <div class="col-md-6">
                                    <input type="number" class="form-control" id="longitude" name="longitude" placeholder="经度" value="{{ longitude }}" step="0.0001">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label class="form-label">系统容量 (kWp)</label>
                            <select class="form-select" id="system_capacity" name="capacity_strings">
                                <!-- 容量选项将通过JavaScript动态填充 -->
                                <option value="">加载中...</option>
                            </select>
                            <small class="form-text text-muted">选择系统容量，基于标准模块（300W）和组串配置</small>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label class="form-label">组件温度系数 (%/°C)</label>
                            <input type="number" class="form-control" id="temp_coeff" name="temp_coeff" value="{{ temp_coeff }}" step="0.01">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label class="form-label">系统损耗 (%)</label>
                            <input type="number" class="form-control" id="system_loss" name="system_loss" value="{{ system_loss }}" step="1">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label class="form-label">异常检测阈值 (MAE)</label>
                            <input type="number" class="form-control" id="fault_threshold" name="fault_threshold" value="{{ fault_threshold }}" step="10">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12 text-center">
                            <button type="submit" class="btn btn-primary">应用设置</button>
                        </div>
                    </div>
                </form>
                <div class="mt-3" id="settings-feedback"></div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">数据导入/导出</div>
            <div class="card-body">
                <form>
                    <div class="mb-3">
                        <label class="form-label">导入数据文件</label>
                        <input type="file" class="form-control" id="upload-data">
                    </div>
                    <div class="mb-3" id="upload-feedback"></div>
                    <div class="mb-3">
                        <label class="form-label">导出数据</label>
                        <div class="row">
                            <div class="col-md-6">
                                <button type="button" class="btn btn-info" id="export-simulation">导出模拟结果</button>
                            </div>
                            <div class="col-md-6">
                                <button type="button" class="btn btn-info" id="export-fault">导出故障分析</button>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3" id="export-feedback"></div>
                </form>
            </div>
        </div>
        <div class="card">
            <div class="card-header">系统维护</div>
            <div class="card-body">
                <form>
                    <div class="mb-3">
                        <label class="form-label">重新训练故障检测模型</label>
                        <button type="button" class="btn btn-warning" id="train-model">训练模型</button>
                    </div>
                    <div class="mb-3" id="train-feedback"></div>
                    <div class="mb-3">
                        <label class="form-label">系统重置</label>
                        <button type="button" class="btn btn-danger" id="reset-system">重置系统</button>
                    </div>
                    <div class="mb-3" id="reset-feedback"></div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 加载容量选项
        loadCapacityOptions();
        
        // 设置表单提交处理
        const settingsForm = document.getElementById('system-settings-form');
        const settingsFeedback = document.getElementById('settings-feedback');
        
        if (settingsForm) {
            settingsForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                // 获取表单数据
                const formData = {
                    latitude: parseFloat(document.getElementById('latitude').value),
                    longitude: parseFloat(document.getElementById('longitude').value),
                    capacity_strings: document.getElementById('system_capacity').value, // 使用capacity_strings代替system_capacity
                    temp_coeff: parseFloat(document.getElementById('temp_coeff').value),
                    system_loss: parseFloat(document.getElementById('system_loss').value),
                    fault_threshold: parseFloat(document.getElementById('fault_threshold').value)
                };
                
                // 发送API请求
                fetch('/api/apply-settings/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: JSON.stringify(formData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        settingsFeedback.innerHTML = `<div class="alert alert-success">${data.message}</div>`;
                    } else {
                        settingsFeedback.innerHTML = `<div class="alert alert-danger">${data.message}</div>`;
                    }
                })
                .catch(error => {
                    console.error('应用设置失败:', error);
                    settingsFeedback.innerHTML = '<div class="alert alert-danger">设置应用失败，请查看控制台错误。</div>';
                });
            });
        }
        
        // 文件上传功能（示例）
        const uploadData = document.getElementById('upload-data');
        const uploadFeedback = document.getElementById('upload-feedback');
        
        if (uploadData) {
            uploadData.addEventListener('change', function() {
                if (this.files.length > 0) {
                    uploadFeedback.innerHTML = '<div class="alert alert-info">文件上传功能尚未实现。</div>';
                }
            });
        }
        
        // 导出功能（示例）
        const exportSimulation = document.getElementById('export-simulation');
        const exportFault = document.getElementById('export-fault');
        const exportFeedback = document.getElementById('export-feedback');
        
        if (exportSimulation) {
            exportSimulation.addEventListener('click', function() {
                exportFeedback.innerHTML = '<div class="alert alert-info">导出仿真结果功能尚未实现。</div>';
            });
        }
        
        if (exportFault) {
            exportFault.addEventListener('click', function() {
                exportFeedback.innerHTML = '<div class="alert alert-info">导出故障分析功能尚未实现。</div>';
            });
        }
        
        // 训练模型功能（示例）
        const trainModel = document.getElementById('train-model');
        const trainFeedback = document.getElementById('train-feedback');
        
        if (trainModel) {
            trainModel.addEventListener('click', function() {
                trainFeedback.innerHTML = '<div class="alert alert-info">模型训练功能尚未实现。</div>';
            });
        }
        
        // 系统重置功能（示例）
        const resetSystem = document.getElementById('reset-system');
        const resetFeedback = document.getElementById('reset-feedback');
        
        if (resetSystem) {
            resetSystem.addEventListener('click', function() {
                // 显示加载中状态
                resetFeedback.innerHTML = '<div class="alert alert-info">正在重置系统，请稍候...</div>';
                
                // 获取CSRF令牌
                const csrftoken = getCookie('csrftoken');
                
                // 发送重置请求
                fetch('/api/reset-simulation/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': csrftoken
                    },
                    body: JSON.stringify({})
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        resetFeedback.innerHTML = `<div class="alert alert-success">${data.message}</div>`;
                        // 重新加载页面以显示新数据
                        setTimeout(() => {
                            window.location.href = '/';
                        }, 2000);
                    } else {
                        resetFeedback.innerHTML = `<div class="alert alert-danger">重置失败: ${data.message}</div>`;
                    }
                })
                .catch(error => {
                    resetFeedback.innerHTML = `<div class="alert alert-danger">发生错误: ${error}</div>`;
                });
            });
        }
    });
    
    // 获取CSRF Token的辅助函数
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
    
    // 加载系统容量选项
    function loadCapacityOptions() {
        const capacitySelect = document.getElementById('system_capacity');
        if (!capacitySelect) return;
        
        // 显示加载中状态
        capacitySelect.innerHTML = '<option value="">加载中...</option>';
        
        // 从API获取容量选项
        fetch('/api/capacity-options/')
            .then(response => response.json())
            .then(data => {
                if (data.options && Array.isArray(data.options)) {
                    // 清空现有选项
                    capacitySelect.innerHTML = '';
                    
                    // 添加提示选项
                    capacitySelect.innerHTML = '<option value="">请选择系统容量...</option>';
                    
                    // 添加所有可用选项
                    data.options.forEach(option => {
                        const optionElement = document.createElement('option');
                        optionElement.value = option.num_strings;
                        optionElement.textContent = option.description;
                        
                        // 如果当前组串数与选项匹配，则选中该选项
                        if (parseInt(option.num_strings) === {{ strings_count|default:1 }}) {
                            optionElement.selected = true;
                        }
                        
                        capacitySelect.appendChild(optionElement);
                    });
                    
                    console.log('系统容量选项已加载');
                } else {
                    capacitySelect.innerHTML = '<option value="">无法加载选项</option>';
                    console.error('API返回的容量选项格式不正确', data);
                }
            })
            .catch(error => {
                capacitySelect.innerHTML = '<option value="">加载选项失败</option>';
                console.error('获取系统容量选项失败:', error);
            });
    }
</script>
{% endblock %} 