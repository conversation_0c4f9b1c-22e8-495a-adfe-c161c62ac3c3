"""
天气数据桥接模块

提供Django天气数据与src模块天气数据之间的桥接功能。
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
import sys
import os


class WeatherDataBridge:
    """天气数据桥接器"""
    
    def __init__(self):
        """初始化天气数据桥接器"""
        self.meteorological_module = None
        self._initialize_meteorological_module()
    
    def _initialize_meteorological_module(self):
        """初始化气象数据模块"""
        try:
            # 确保src路径在sys.path中
            from .config import SimulationConfig
            src_path = SimulationConfig.get_src_path()
            if src_path not in sys.path:
                sys.path.append(src_path)
            
            # 导入气象数据模块
            from data_layer.meteorological_data_module import MeteorologicalDataModule
            self.meteorological_module = MeteorologicalDataModule()
            print("气象数据模块初始化成功")
            
        except ImportError as e:
            print(f"警告: 无法导入气象数据模块: {e}")
            self.meteorological_module = None
        except Exception as e:
            print(f"气象数据模块初始化错误: {e}")
            self.meteorological_module = None
    
    def generate_weather_data(self, 
                            start_time: datetime, 
                            end_time: datetime, 
                            freq: str = 'H',
                            latitude: float = 39.9,
                            longitude: float = 116.4,
                            altitude: float = 50) -> pd.DataFrame:
        """
        生成天气数据
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            freq: 频率 ('H'=小时, '30T'=30分钟等)
            latitude: 纬度
            longitude: 经度
            altitude: 海拔
            
        Returns:
            pd.DataFrame: 天气数据
        """
        try:
            # 创建时间索引
            times = pd.date_range(start=start_time, end=end_time, freq=freq, tz='Asia/Shanghai')
            
            if self.meteorological_module:
                # 使用真实的气象数据模块
                return self._generate_with_meteorological_module(times, latitude, longitude, altitude)
            else:
                # 使用简化的天气数据生成
                return self._generate_simple_weather_data(times, latitude, longitude)
                
        except Exception as e:
            print(f"天气数据生成错误: {e}")
            return self._generate_fallback_weather_data(start_time, end_time, freq)
    
    def _generate_with_meteorological_module(self, 
                                           times: pd.DatetimeIndex,
                                           latitude: float,
                                           longitude: float,
                                           altitude: float) -> pd.DataFrame:
        """
        使用气象数据模块生成天气数据
        
        Args:
            times: 时间索引
            latitude: 纬度
            longitude: 经度
            altitude: 海拔
            
        Returns:
            pd.DataFrame: 天气数据
        """
        try:
            # 使用气象数据模块获取辐照度数据
            irradiance_data = self.meteorological_module.get_irradiance(
                latitude, longitude, altitude, times
            )
            
            # 获取环境数据
            environmental_data = self.meteorological_module.get_environmental_conditions(times)
            
            # 合并数据
            weather_data = pd.DataFrame(index=times)
            
            # 辐照度数据
            if 'ghi' in irradiance_data.columns:
                weather_data['ghi'] = irradiance_data['ghi']
            if 'dni' in irradiance_data.columns:
                weather_data['dni'] = irradiance_data['dni']
            if 'dhi' in irradiance_data.columns:
                weather_data['dhi'] = irradiance_data['dhi']
            
            # 环境数据
            if 'temp_air' in environmental_data.columns:
                weather_data['temp_air'] = environmental_data['temp_air']
            if 'wind_speed' in environmental_data.columns:
                weather_data['wind_speed'] = environmental_data['wind_speed']
            
            # 填充缺失值
            weather_data = self._fill_missing_values(weather_data)
            
            return weather_data
            
        except Exception as e:
            print(f"使用气象数据模块生成天气数据失败: {e}")
            return self._generate_simple_weather_data(times, latitude, longitude)
    
    def _generate_simple_weather_data(self, 
                                    times: pd.DatetimeIndex,
                                    latitude: float,
                                    longitude: float) -> pd.DataFrame:
        """
        生成简化的天气数据
        
        Args:
            times: 时间索引
            latitude: 纬度
            longitude: 经度
            
        Returns:
            pd.DataFrame: 简化的天气数据
        """
        try:
            weather_data = pd.DataFrame(index=times)
            
            # 基于时间和位置的简化模型
            hours = times.hour
            days = times.dayofyear
            
            # GHI模拟（基于太阳高度角的简化模型）
            # 这是一个非常简化的模型，实际应用中应该使用更精确的太阳辐射模型
            solar_elevation = self._calculate_solar_elevation(times, latitude, longitude)
            ghi = np.maximum(0, solar_elevation * 10)  # 简化的GHI计算
            
            # 添加一些随机变化来模拟云层影响
            cloud_factor = 0.7 + 0.3 * np.random.random(len(times))
            ghi = ghi * cloud_factor
            
            weather_data['ghi'] = ghi
            weather_data['dni'] = ghi * 0.8  # DNI约为GHI的80%
            weather_data['dhi'] = ghi * 0.2  # DHI约为GHI的20%
            
            # 温度模拟（基于时间的简化模型）
            base_temp = 20 + 10 * np.sin(2 * np.pi * days / 365)  # 年度变化
            daily_temp = 5 * np.sin(2 * np.pi * hours / 24)  # 日变化
            temp_noise = 2 * np.random.random(len(times)) - 1  # 随机噪声
            weather_data['temp_air'] = base_temp + daily_temp + temp_noise
            
            # 风速模拟
            weather_data['wind_speed'] = 1 + 3 * np.random.random(len(times))
            
            return weather_data
            
        except Exception as e:
            print(f"简化天气数据生成错误: {e}")
            return self._generate_fallback_weather_data(times[0], times[-1], 'H')
    
    def _calculate_solar_elevation(self, 
                                 times: pd.DatetimeIndex,
                                 latitude: float,
                                 longitude: float) -> np.ndarray:
        """
        计算太阳高度角（简化版本）
        
        Args:
            times: 时间索引
            latitude: 纬度
            longitude: 经度
            
        Returns:
            np.ndarray: 太阳高度角数组
        """
        try:
            # 这是一个非常简化的太阳高度角计算
            # 实际应用中应该使用更精确的天文算法
            
            hours = times.hour + times.minute / 60.0
            days = times.dayofyear
            
            # 简化的太阳赤纬角
            declination = 23.45 * np.sin(np.radians(360 * (284 + days) / 365))
            
            # 简化的时角
            hour_angle = 15 * (hours - 12)
            
            # 简化的太阳高度角
            lat_rad = np.radians(latitude)
            dec_rad = np.radians(declination)
            hour_rad = np.radians(hour_angle)
            
            elevation = np.arcsin(
                np.sin(lat_rad) * np.sin(dec_rad) + 
                np.cos(lat_rad) * np.cos(dec_rad) * np.cos(hour_rad)
            )
            
            return np.degrees(elevation)
            
        except Exception as e:
            print(f"太阳高度角计算错误: {e}")
            # 返回简化的日变化模式
            hours = times.hour
            return np.maximum(0, 60 * np.sin(np.pi * (hours - 6) / 12))
    
    def _fill_missing_values(self, weather_data: pd.DataFrame) -> pd.DataFrame:
        """
        填充天气数据中的缺失值
        
        Args:
            weather_data: 天气数据DataFrame
            
        Returns:
            pd.DataFrame: 填充后的天气数据
        """
        # 默认值
        defaults = {
            'ghi': 0,
            'dni': 0,
            'dhi': 0,
            'temp_air': 25,
            'wind_speed': 1
        }
        
        for column, default_value in defaults.items():
            if column not in weather_data.columns:
                weather_data[column] = default_value
            else:
                weather_data[column] = weather_data[column].fillna(default_value)
        
        return weather_data
    
    def _generate_fallback_weather_data(self, 
                                      start_time: datetime,
                                      end_time: datetime,
                                      freq: str) -> pd.DataFrame:
        """
        生成备用天气数据（当所有其他方法都失败时）
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            freq: 频率
            
        Returns:
            pd.DataFrame: 备用天气数据
        """
        try:
            times = pd.date_range(start=start_time, end=end_time, freq=freq)
            
            # 创建基本的天气数据
            weather_data = pd.DataFrame({
                'ghi': [500] * len(times),  # 固定GHI值
                'dni': [400] * len(times),  # 固定DNI值
                'dhi': [100] * len(times),  # 固定DHI值
                'temp_air': [25] * len(times),  # 固定温度
                'wind_speed': [2] * len(times)  # 固定风速
            }, index=times)
            
            return weather_data
            
        except Exception as e:
            print(f"备用天气数据生成错误: {e}")
            # 最后的备用方案：单点数据
            return pd.DataFrame({
                'ghi': [0],
                'dni': [0],
                'dhi': [0],
                'temp_air': [25],
                'wind_speed': [1]
            }, index=pd.DatetimeIndex([datetime.now()]))
    
    def convert_to_src_format(self, django_weather: pd.DataFrame) -> pd.DataFrame:
        """
        将Django格式的天气数据转换为src模块格式
        
        Args:
            django_weather: Django格式的天气数据
            
        Returns:
            pd.DataFrame: src模块格式的天气数据
        """
        try:
            # 使用数据格式转换器
            from .data_format_converter import DataFormatConverter
            return DataFormatConverter.django_to_pvlib_weather(django_weather)
            
        except Exception as e:
            print(f"天气数据格式转换错误: {e}")
            return django_weather
    
    def validate_weather_data(self, weather_data: pd.DataFrame) -> bool:
        """
        验证天气数据的有效性
        
        Args:
            weather_data: 天气数据
            
        Returns:
            bool: 数据是否有效
        """
        try:
            required_columns = ['ghi', 'temp_air', 'wind_speed']
            
            # 检查必要的列是否存在
            for col in required_columns:
                if col not in weather_data.columns:
                    print(f"缺少必要的天气数据列: {col}")
                    return False
            
            # 检查数据范围是否合理
            if weather_data['ghi'].min() < 0 or weather_data['ghi'].max() > 1500:
                print("GHI数据范围不合理")
                return False
            
            if weather_data['temp_air'].min() < -50 or weather_data['temp_air'].max() > 60:
                print("温度数据范围不合理")
                return False
            
            if weather_data['wind_speed'].min() < 0 or weather_data['wind_speed'].max() > 50:
                print("风速数据范围不合理")
                return False
            
            return True
            
        except Exception as e:
            print(f"天气数据验证错误: {e}")
            return False
