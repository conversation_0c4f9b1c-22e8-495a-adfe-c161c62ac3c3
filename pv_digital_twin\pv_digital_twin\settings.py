"""
Django settings for pv_digital_twin project.

Generated by 'django-admin startproject' using Django 4.2.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""

import os
import sys
from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# 添加src目录到Python路径
SRC_DIR = BASE_DIR.parent / 'src'
if str(SRC_DIR) not in sys.path:
    sys.path.append(str(SRC_DIR))


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "django-insecure-0v@$_g^(wm)8_n!90l2&4j_p!k+(e%d5woe#8vw1d01l(bk6+!"

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ['localhost', '127.0.0.1', 'testserver']


# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "dashboard",
    "api",
]

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

ROOT_URLCONF = "pv_digital_twin.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [os.path.join(BASE_DIR, "templates")],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "pv_digital_twin.wsgi.application"


# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.sqlite3",
        "NAME": BASE_DIR / "db.sqlite3",
    }
}


# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = "zh-hans"

TIME_ZONE = "Asia/Shanghai"

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

STATIC_URL = "static/"
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, "static"),
]

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# 仿真系统配置
# 控制是否使用真实仿真系统，优先从环境变量读取
# 支持的环境变量值：'true', '1', 'yes', 'on' 表示启用真实仿真系统
# 其他值或未设置则使用模拟仿真系统
USE_REAL_SIMULATION = os.getenv('USE_REAL_SIMULATION', 'false').lower() in ['true', '1', 'yes', 'on']

SIMULATION_CONFIG = {
    # 基础配置
    'USE_REAL_SIMULATION': USE_REAL_SIMULATION,  # 动态从环境变量获取
    'MODEL_PATH': BASE_DIR.parent / 'models',    # 异常检测模型文件存储路径
    'CACHE_TIMEOUT': 300,                        # 数据缓存超时时间（秒）
    'MAX_SIMULATION_POINTS': 180,                # 单次仿真最大数据点数，防止内存溢出
    'RESPONSE_TIME_LIMIT': 2.0,                  # API响应时间限制，超时自动降级
    'SRC_PATH': SRC_DIR,                         # 真实仿真系统源代码路径

    # 阶段四新增：仿真控制配置
    'SIMULATION_CONTROL': {
        'ENABLE_PAUSE_RESUME': True,             # 启用高级仿真暂停/恢复功能
        'ENABLE_DYNAMIC_PARAMS': True,           # 启用仿真参数动态调整功能
        'AUTO_SAVE_INTERVAL': 3600,              # 仿真数据自动保存间隔时间（秒）
        'MAX_MEMORY_USAGE_MB': 512,              # 仿真引擎最大内存使用限制（MB）
        'MULTI_TIME_SCALE': {
            'ENABLE': True,                      # 是否启用多时间尺度仿真
            'DEFAULT_SCALE': 'hourly',           # 默认仿真时间尺度
            'AVAILABLE_SCALES': ['secondly', 'minutely', 'hourly', 'daily']  # 支持的时间尺度列表
        }
    },

    # 阶段四新增：数据持久化配置
    'DATA_PERSISTENCE': {
        'ENABLE': True,                          # 是否启用数据持久化功能
        'STORAGE_TYPE': 'database',              # 数据存储类型（database/file/memory）
        'MAX_HISTORY_DAYS': 30,                  # 历史数据最大保存天数
        'COMPRESSION': True,                     # 是否启用数据压缩存储
        'BACKUP_INTERVAL': 86400,                # 数据自动备份间隔时间（秒）
    }
}
