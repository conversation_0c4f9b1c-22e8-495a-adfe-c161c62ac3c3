<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}光伏数字孪生平台{% endblock %}</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <!-- ECharts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center my-4">光伏数字孪生平台</h1>
            </div>
        </div>
        
        <ul class="nav nav-tabs mb-4">
            <li class="nav-item">
                <a class="nav-link {% if active_tab == 'dashboard' %}active{% endif %}" href="{% url 'dashboard:dashboard' %}">系统仪表盘</a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if active_tab == 'fault_diagnosis' %}active{% endif %}" href="{% url 'dashboard:fault_diagnosis' %}">故障诊断</a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if active_tab == 'system_settings' %}active{% endif %}" href="{% url 'dashboard:system_settings' %}">系统设置</a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if active_tab == 'simulation_logs' %}active{% endif %}" href="{% url 'dashboard:simulation_logs' %}">
                    <i class="bi bi-terminal"></i> 仿真日志
                </a>
            </li>
        </ul>
        
        <div class="content">
            {% block content %}{% endblock %}
        </div>
    </div>
    
    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html> 