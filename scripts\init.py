#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
光伏数字孪生平台初始化脚本

此脚本在首次使用时运行，创建必要的目录结构和初始化设置。
"""

import os
import sys
import shutil
from pathlib import Path
import argparse


def create_directory_structure():
    """创建项目的目录结构"""
    print("创建目录结构...")

    # 创建主要目录
    directories = ["data/raw", "data/processed", "detect", "static/images"]

    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"  创建 {directory}")

    return True


def initialize_detect_module():
    """初始化故障检测模块"""
    print("初始化故障检测模块...")

    detect_dir = "detect"
    model_file = os.path.join(detect_dir, "lstm_kat_autoencoder.pth")

    # 检查模型文件是否已存在
    if os.path.exists(model_file):
        print(f"  故障检测模型已存在: {model_file}")
        return True

    # 创建空的占位符模型文件
    try:
        with open(model_file, "wb") as f:
            # 写入空文件作为占位符
            pass
        print(f"  创建空的模型占位符: {model_file}")
        print(
            "  注意: 这只是一个占位符，实际的故障检测功能需要训练或提供一个真实的模型文件。"
        )
        return True
    except Exception as e:
        print(f"  创建模型占位符失败: {e}")
        return False


def check_requirements():
    """检查依赖项"""
    print("检查依赖项...")
    req_path = Path("requirements.txt")

    if not req_path.exists():
        print("  未找到requirements.txt，创建基本版本...")
        with open(req_path, "w") as f:
            f.write(
                "\n".join(
                    [
                        "pvlib>=0.10.0",
                        "numpy>=1.20.0",
                        "pandas>=1.3.0",
                        "matplotlib>=3.5.0",
                        "flask>=2.2.0",
                        "scipy>=1.8.0",
                        "pytz>=2022.1",
                        "requests>=2.28.0",
                        "Django==4.2.7",
                    ]
                )
            )
        print("  创建requirements.txt完成")
    else:
        print("  requirements.txt已存在")

    return True


def install_requirements():
    """安装依赖项"""
    print("是否安装依赖项? (y/n)")
    choice = input().lower()
    if choice in ["y", "yes"]:
        print("安装依赖项...")
        try:
            import pip

            # 使用subprocess运行pip以避免在同一进程中导入问题
            import subprocess

            subprocess.run(
                [sys.executable, "-m", "pip", "install", "-r", "requirements.txt"],
                check=True,
            )
            print("依赖项安装完成")
            return True
        except Exception as e:
            print(f"安装依赖项时出错: {e}")
            return False
    else:
        print("跳过依赖项安装")
        return True


def check_files():
    """检查必要的文件是否存在"""
    print("检查必要文件...")

    required_files = [
        "pv_digital_twin/manage.py",
        "src/model/pv_model.py",
        "src/utils/data_utils.py",
        "src/model/fault_diagnosis.py",
    ]

    all_files_exist = True
    for file in required_files:
        if not os.path.exists(file):
            print(f"  警告: {file} 不存在")
            all_files_exist = False
        else:
            print(f"  {file} 已存在")

    if not all_files_exist:
        print("  某些必要文件不存在，请确保所有源文件已正确下载")

    return all_files_exist


def run_tests():
    """运行系统测试"""
    print("是否运行系统测试? (y/n)")
    choice = input().lower()
    if choice in ["y", "yes"]:
        print("运行系统测试...")
        test_file = Path("test_system.py")

        if not test_file.exists():
            print("  未找到测试文件: test_system.py，跳过测试")
            return True

        try:
            import unittest

            # 使用subprocess运行测试以避免导入问题
            import subprocess

            subprocess.run([sys.executable, "test_system.py"], check=False)
            print("测试完成")
            return True
        except Exception as e:
            print(f"运行测试时出错: {e}")
            return False
    else:
        print("跳过系统测试")
        return True


def print_welcome_message():
    """打印欢迎信息"""
    print("\n" + "=" * 70)
    print(" 光伏数字孪生平台 - 初始化完成 ".center(70, "="))
    print("=" * 70 + "\n")
    print("系统已成功初始化！可以通过以下方式开始使用:")
    print("\n1. 启动Django开发服务器:")
    print("   cd pv_digital_twin")
    print("   python manage.py runserver")
    print("\n访问应用: http://127.0.0.1:8000/")
    print("\n祝您使用愉快！")
    print("\n" + "=" * 70 + "\n")


def main():
    parser = argparse.ArgumentParser(description="光伏数字孪生平台初始化工具")
    parser.add_argument(
        "--no-input", action="store_true", help="无交互模式，使用默认选项"
    )
    args = parser.parse_args()

    print("\n" + "=" * 70)
    print(" 光伏数字孪生平台 - 初始化 ".center(70, "="))
    print("=" * 70 + "\n")

    # 执行初始化步骤
    steps = [
        ("创建目录结构", create_directory_structure),
        ("检查必要文件", check_files),
        ("初始化故障检测模块", initialize_detect_module),
        ("检查依赖项", check_requirements),
    ]

    # 如果不是无交互模式，添加可选步骤
    if not args.no_input:
        steps.extend(
            [
                ("安装依赖项", install_requirements),
                ("运行系统测试", run_tests),
            ]
        )

    # 执行所有步骤
    for name, func in steps:
        print(f"\n{name}...")
        if not func():
            print(f"\n错误: {name}失败，请修复上述错误后重试。")
            return 1

    # 打印欢迎信息
    print_welcome_message()
    return 0


if __name__ == "__main__":
    sys.exit(main())
