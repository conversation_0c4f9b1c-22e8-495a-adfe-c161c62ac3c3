#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
光伏数字孪生平台功能测试脚本

此脚本测试系统的主要组件和功能，确保各部分正常工作。
"""

import os
import sys
import unittest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta


# 测试用例类
class PVDigitalTwinTests(unittest.TestCase):
    """光伏数字孪生平台测试类"""

    def setUp(self):
        """测试前准备工作"""
        # 确保可以导入项目模块
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

        # 检查必要文件是否存在
        self.assertTrue(
            os.path.exists("src/model/pv_model.py"), "找不到pv_model.py模型文件"
        )
        self.assertTrue(
            os.path.exists("src/utils/data_utils.py"), "找不到data_utils.py数据工具文件"
        )
        self.assertTrue(
            os.path.exists("src/model/fault_diagnosis.py"),
            "找不到fault_diagnosis.py故障诊断文件",
        )

        print("\n开始测试光伏数字孪生平台组件...")

    def test_pv_model(self):
        """测试光伏系统模型"""
        print("\n测试 PV 模型...")
        try:
            from src.model.pv_model import PVDigitalTwin, get_solar_position

            # 创建测试模型
            model = PVDigitalTwin(latitude=40.0, longitude=116.0, name="测试系统")

            # 测试清晰天空模拟功能
            today = datetime.now().strftime("%Y-%m-%d")
            tomorrow = (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")
            results = model.simulate_clear_sky(today, tomorrow, freq="1h")

            # 验证结果
            self.assertIsInstance(
                results, pd.DataFrame, "模拟结果应该是pandas DataFrame"
            )
            self.assertGreater(len(results), 20, "模拟结果应该至少包含24小时的数据")
            self.assertIn("ac_power", results.columns, "模拟结果应该包含ac_power列")

            # 测试太阳位置计算
            solar_pos = get_solar_position(40.0, 116.0, today)
            self.assertIsInstance(
                solar_pos, pd.DataFrame, "太阳位置结果应该是pandas DataFrame"
            )
            self.assertIn("zenith", solar_pos.columns, "太阳位置结果应该包含zenith列")
            self.assertIn("azimuth", solar_pos.columns, "太阳位置结果应该包含azimuth列")

            print("PV 模型测试通过")
        except ImportError:
            self.fail("无法导入PV模型模块")
        except Exception as e:
            self.fail(f"PV模型测试失败: {e}")

    def test_data_utils(self):
        """测试数据工具模块"""
        print("\n测试数据工具...")
        try:
            from src.utils.data_utils import (
                generate_synthetic_weather_data,
                detect_anomalies,
            )

            # 测试生成合成气象数据
            today = datetime.now().strftime("%Y-%m-%d")
            tomorrow = (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")

            weather_data = generate_synthetic_weather_data(
                start_date=today,
                end_date=tomorrow,
                latitude=40.0,
                longitude=116.0,
                freq="1h",
            )

            # 验证结果
            self.assertIsInstance(
                weather_data, pd.DataFrame, "生成的气象数据应该是pandas DataFrame"
            )
            self.assertGreater(
                len(weather_data), 20, "气象数据应该至少包含24小时的数据"
            )
            self.assertIn("ghi", weather_data.columns, "气象数据应该包含ghi列")

            # 测试异常检测
            # 创建一些测试数据，包括一些异常值
            data = np.sin(np.linspace(0, 10 * np.pi, 100)) * 100
            data[30:35] = 250  # 添加几个异常值

            anomalies = detect_anomalies(data, window_size=10, threshold=2)

            # 验证结果
            self.assertIsInstance(anomalies, np.ndarray, "异常检测结果应该是numpy数组")
            self.assertEqual(len(anomalies), 100, "异常检测结果应该与输入数据长度一致")
            self.assertTrue(np.any(anomalies[30:35]), "应该检测到添加的异常值")

            print("数据工具测试通过")
        except ImportError:
            self.fail("无法导入数据工具模块")
        except Exception as e:
            self.fail(f"数据工具测试失败: {e}")

    def test_fault_diagnosis(self):
        """测试故障诊断模块"""
        print("\n测试故障诊断模块... (不加载模型)")
        try:
            from src.model.fault_diagnosis import FaultDiagnosisSystem

            # 创建故障诊断系统（不加载模型）
            fault_system = FaultDiagnosisSystem()

            # 测试故障原因分析功能
            mock_anomaly_data = pd.DataFrame(
                {
                    "AC_POWER": [100, 200, 300, 50, 400],
                    "real_AC_POWER": [200, 200, 300, 200, 400],
                    "error_AC_POWER": [100, 0, 0, 150, 0],
                    "AMBIENT_TEMPERATURE": [25, 25, 30, 35, 25],
                    "real_AMBIENT_TEMPERATURE": [25, 25, 30, 35, 25],
                    "MODULE_TEMPERATURE": [40, 40, 50, 80, 40],
                    "real_MODULE_TEMPERATURE": [40, 40, 50, 40, 40],
                    "error_MODULE_TEMPERATURE": [0, 0, 0, 40, 0],
                    "IRRADIATION": [800, 800, 900, 900, 800],
                    "real_IRRADIATION": [800, 800, 900, 900, 800],
                    "threshold": [50, 50, 50, 50, 50],
                    "is_anomaly": [1, 0, 0, 1, 0],
                }
            )

            # 测试故障原因分析
            causes = fault_system.analyze_fault_cause(mock_anomaly_data)

            # 验证结果
            self.assertIsInstance(causes, list, "故障原因结果应该是列表")
            self.assertGreater(len(causes), 0, "应该至少有一个故障原因")

            # 测试建议操作
            recommendations = fault_system.recommend_actions(causes)

            # 验证结果
            self.assertIsInstance(recommendations, list, "建议操作结果应该是列表")
            self.assertGreater(len(recommendations), 0, "应该至少有一个建议操作")

            print("故障诊断模块基本功能测试通过")
        except ImportError:
            self.fail("无法导入故障诊断模块")
        except Exception as e:
            self.fail(f"故障诊断测试失败: {e}")

    def test_django_app(self):
        """测试Django应用是否可以正确导入"""
        print("\n测试Django应用导入...")
        try:
            # 尝试导入Django应用主文件
            sys.path.append(os.getcwd())
            from pv_digital_twin import settings
            from pv_digital_twin.dashboard.pv_model_adapter import PVModelAdapter

            # 检查关键组件是否存在
            self.assertTrue(
                hasattr(settings, "DATABASES"), "settings模块应该包含DATABASES配置"
            )
            self.assertTrue(
                hasattr(PVModelAdapter, "get_instance"),
                "PVModelAdapter应该包含get_instance方法",
            )

            print("Django应用导入测试通过")
        except ImportError as e:
            self.fail(f"无法导入Django应用: {e}")
        except Exception as e:
            self.fail(f"Django应用测试失败: {e}")


def main():
    """主函数"""
    # 运行所有测试
    unittest.main(argv=["first-arg-is-ignored"], exit=False)


if __name__ == "__main__":
    main()
