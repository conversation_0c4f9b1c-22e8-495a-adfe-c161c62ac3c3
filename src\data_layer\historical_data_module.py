"""
历史数据模块 - 用于处理光伏系统的历史运行数据

本模块提供了加载、预处理、特征提取和管理光伏系统历史数据的功能。
支持多种数据源和格式，并提供数据质量评估和特征工程功能。
"""

import os
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Union, Optional, Any, Callable

import numpy as np
import pandas as pd
from sklearn.preprocessing import StandardScaler, MinMaxScaler
import h5py

# 如果可用，导入Dask进行大数据处理
try:
    import dask.dataframe as dd

    DASK_AVAILABLE = True
except ImportError:
    DASK_AVAILABLE = False
    logging.warning("Dask未安装，大规模数据处理将回退到Pandas")

# 导入工具函数
from ..utils.data_utils import (
    load_data,
    preprocess_data,
    standardize_data,
    extract_features,
    assess_data_quality,
    split_data,
)


class HistoricalDataModule:
    """
    历史数据模块，用于管理光伏系统的历史运行数据

    功能:
    1. 数据加载: 支持多种格式和数据源
    2. 数据预处理: 缺失值处理、异常值检测、时间对齐等
    3. 特征工程: 特征提取、特征选择、特征变换
    4. 数据分割: 训练集/验证集/测试集划分
    5. 数据质量评估: 完整性、一致性、准确性评估
    """

    def __init__(self, config: Optional[Dict] = None):
        """
        初始化历史数据模块

        参数:
        -----
        config : Dict, 可选
            配置参数字典，包含数据路径、预处理选项等
        """
        self.config = config or {}
        self.data = {}  # 存储不同数据集
        self.scalers = {}  # 存储不同特征的缩放器
        self.feature_extractors = {}  # 存储特征提取器
        self.metadata = {
            "last_updated": datetime.now().isoformat(),
            "datasets": {},
            "data_quality": {},
        }

        # 设置日志记录器
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.INFO)
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)

        # 加载默认配置
        self._load_default_config()

    def _load_default_config(self):
        """加载默认配置参数"""
        default_config = {
            "data_paths": {
                "pv_system": "data/pv_system/",
                "weather": "data/weather/",
                "anomaly": "data/anomaly/",
            },
            "preprocessing": {
                "handle_missing": "interpolate",
                "detect_outliers": True,
                "outlier_method": "zscore",
                "outlier_threshold": 3.0,
            },
            "feature_extraction": {
                "window_size": 24,
                "features": [
                    "mean",
                    "std",
                    "min",
                    "max",
                    "quantile_25",
                    "quantile_75",
                    "trend",
                    "rolling_mean",
                    "rolling_std",
                ],
            },
            "data_split": {
                "test_size": 0.2,
                "val_size": 0.1,
                "shuffle": True,
                "random_state": 42,
            },
        }

        # 更新配置，保留用户提供的值
        for key, value in default_config.items():
            if key not in self.config:
                self.config[key] = value
            elif isinstance(value, dict) and isinstance(self.config[key], dict):
                # 递归更新嵌套字典
                for sub_key, sub_value in value.items():
                    if sub_key not in self.config[key]:
                        self.config[key][sub_key] = sub_value

    def load_dataset(
        self,
        name: str,
        file_path: str,
        format: Optional[str] = None,
        time_col: str = "datetime",
        preprocess: bool = True,
        **kwargs,
    ) -> pd.DataFrame:
        """
        加载数据集并进行预处理

        参数:
        -----
        name : str
            数据集名称，用于在内部字典中引用
        file_path : str
            数据文件路径
        format : str, 可选
            文件格式，如果为None则从文件扩展名自动判断
        time_col : str, 可选
            时间列名，默认为'datetime'
        preprocess : bool, 可选
            是否进行预处理，默认为True
        **kwargs :
            传递给load_data函数的其他参数

        返回:
        -----
        pandas.DataFrame
            加载的数据框
        """
        self.logger.info(f"加载数据集: {name} 从 {file_path}")

        # 检查文件是否存在
        if not os.path.exists(file_path):
            self.logger.error(f"文件不存在: {file_path}")
            raise FileNotFoundError(f"文件不存在: {file_path}")

        # 根据文件大小决定使用Pandas还是Dask
        file_size = os.path.getsize(file_path) / (1024 * 1024)  # 转换为MB
        use_dask = (
            file_size > 100 and DASK_AVAILABLE
        )  # 如果文件大于100MB且Dask可用，则使用Dask

        try:
            if use_dask:
                self.logger.info(f"文件大小为 {file_size:.2f}MB，使用Dask加载")
                # 使用Dask加载大文件
                if format is None:
                    _, ext = os.path.splitext(file_path)
                    format = ext.lower()[1:]  # 去掉点号

                if format in ["csv", "txt"]:
                    df = dd.read_csv(file_path, **kwargs).compute()
                elif format == "parquet":
                    df = dd.read_parquet(file_path, **kwargs).compute()
                else:
                    self.logger.warning(f"Dask不支持 {format} 格式，回退到Pandas")
                    df = load_data(file_path, format, **kwargs)
            else:
                # 使用普通的load_data函数
                df = load_data(file_path, format, **kwargs)

            # 记录数据集元数据
            self.metadata["datasets"][name] = {
                "file_path": file_path,
                "format": format,
                "shape": df.shape,
                "columns": df.columns.tolist(),
                "loaded_at": datetime.now().isoformat(),
            }

            # 处理时间列
            if time_col in df.columns:
                df[time_col] = pd.to_datetime(df[time_col])

            # 进行预处理
            if preprocess:
                preprocessing_config = self.config.get("preprocessing", {})
                df = self.preprocess_dataset(
                    df,
                    time_col=time_col,
                    handle_missing=preprocessing_config.get(
                        "handle_missing", "interpolate"
                    ),
                    detect_outliers=preprocessing_config.get("detect_outliers", True),
                    outlier_method=preprocessing_config.get("outlier_method", "zscore"),
                    outlier_threshold=preprocessing_config.get(
                        "outlier_threshold", 3.0
                    ),
                )

            # 存储数据集
            self.data[name] = df

            return df

        except Exception as e:
            self.logger.error(f"加载数据集 {name} 失败: {str(e)}")
            raise

    def load_multiple_datasets(
        self,
        dataset_configs: List[Dict],
        merge: bool = False,
        merge_on: Optional[str] = None,
    ) -> Dict[str, pd.DataFrame]:
        """
        加载多个数据集

        参数:
        -----
        dataset_configs : List[Dict]
            数据集配置列表，每个配置是一个字典，包含'name'和'file_path'等键
        merge : bool, 可选
            是否合并数据集，默认为False
        merge_on : str, 可选
            合并数据集的列名，默认为None

        返回:
        -----
        Dict[str, pd.DataFrame] 或 pd.DataFrame
            如果merge=False，返回数据集字典；否则返回合并后的数据框
        """
        loaded_datasets = {}

        for config in dataset_configs:
            name = config.pop("name")
            file_path = config.pop("file_path")
            df = self.load_dataset(name, file_path, **config)
            loaded_datasets[name] = df

        if merge and len(loaded_datasets) > 1:
            self.logger.info(f"合并 {len(loaded_datasets)} 个数据集")
            merged_df = self._merge_datasets(loaded_datasets, merge_on)
            self.data["merged"] = merged_df
            return merged_df

        return loaded_datasets

    def _merge_datasets(
        self, datasets: Dict[str, pd.DataFrame], merge_on: Optional[str] = None
    ) -> pd.DataFrame:
        """
        合并多个数据集

        参数:
        -----
        datasets : Dict[str, pd.DataFrame]
            数据集字典
        merge_on : str, 可选
            合并数据集的列名，默认为None

        返回:
        -----
        pd.DataFrame
            合并后的数据框
        """
        if not datasets:
            return pd.DataFrame()

        # 获取第一个数据集作为基础
        base_name = list(datasets.keys())[0]
        merged_df = datasets[base_name].copy()

        # 合并其他数据集
        for name, df in datasets.items():
            if name == base_name:
                continue

            if (
                merge_on is not None
                and merge_on in merged_df.columns
                and merge_on in df.columns
            ):
                # 使用指定列进行合并
                merged_df = pd.merge(
                    merged_df, df, on=merge_on, how="outer", suffixes=("", f"_{name}")
                )
            else:
                # 如果没有指定合并列，尝试找到共同的时间列
                common_time_cols = [
                    col
                    for col in ["datetime", "timestamp", "time", "date"]
                    if col in merged_df.columns and col in df.columns
                ]

                if common_time_cols:
                    merge_col = common_time_cols[0]
                    self.logger.info(f"使用 {merge_col} 列合并数据集")
                    merged_df = pd.merge(
                        merged_df,
                        df,
                        on=merge_col,
                        how="outer",
                        suffixes=("", f"_{name}"),
                    )
                else:
                    self.logger.warning(f"无法找到合并列，将使用索引合并 {name} 数据集")
                    # 使用索引合并，可能导致数据不一致
                    merged_df = pd.concat([merged_df, df], axis=1)

        return merged_df

    def preprocess_dataset(
        self,
        df: pd.DataFrame,
        time_col: str = "datetime",
        handle_missing: str = "interpolate",
        detect_outliers: bool = True,
        outlier_method: str = "zscore",
        outlier_threshold: float = 3.0,
    ) -> pd.DataFrame:
        """
        对数据集进行预处理

        参数:
        -----
        df : pd.DataFrame
            要预处理的数据框
        time_col : str, 可选
            时间列名，默认为'datetime'
        handle_missing : str, 可选
            处理缺失值的方法，可选'interpolate', 'ffill', 'bfill', 'drop', 'mean'
        detect_outliers : bool, 可选
            是否检测异常值，默认为True
        outlier_method : str, 可选
            异常值检测方法，可选'zscore', 'iqr'
        outlier_threshold : float, 可选
            异常值检测阈值，默认为3.0

        返回:
        -----
        pd.DataFrame
            预处理后的数据框
        """
        self.logger.info("开始数据预处理")

        # 使用data_utils中的预处理函数
        processed_df, preprocessing_info = preprocess_data(
            df,
            time_col=time_col,
            handle_missing=handle_missing,
            detect_outliers=detect_outliers,
            outlier_method=outlier_method,
            outlier_threshold=outlier_threshold,
        )

        # 评估数据质量
        quality_report = assess_data_quality(processed_df, time_col)

        # 记录数据质量报告
        dataset_hash = hash(tuple(sorted(df.columns)))
        self.metadata["data_quality"][dataset_hash] = {
            "columns": df.columns.tolist(),
            "quality_report": quality_report,
            "preprocessing_params": {
                "handle_missing": handle_missing,
                "detect_outliers": detect_outliers,
                "outlier_method": outlier_method,
                "outlier_threshold": outlier_threshold,
            },
            "preprocessing_info": preprocessing_info,
            "processed_at": datetime.now().isoformat(),
        }

        return processed_df

    def extract_features_from_dataset(
        self,
        df: pd.DataFrame,
        dataset_name: str,
        window_size: Optional[int] = None,
        features: Optional[List[str]] = None,
    ) -> pd.DataFrame:
        """
        从数据集中提取特征

        参数:
        -----
        df : pd.DataFrame
            源数据框
        dataset_name : str
            数据集名称，用于存储特征提取器
        window_size : int, 可选
            滑动窗口大小，如果为None则使用配置中的值
        features : List[str], 可选
            要计算的特征列表，如果为None则使用配置中的值

        返回:
        -----
        pd.DataFrame
            提取的特征数据框
        """
        # 使用配置中的默认值
        if window_size is None:
            window_size = self.config.get("feature_extraction", {}).get(
                "window_size", 24
            )

        if features is None:
            features = self.config.get("feature_extraction", {}).get(
                "features",
                [
                    "mean",
                    "std",
                    "min",
                    "max",
                    "quantile_25",
                    "quantile_75",
                    "trend",
                    "rolling_mean",
                    "rolling_std",
                ],
            )

        self.logger.info(f"从数据集 {dataset_name} 提取特征，窗口大小={window_size}")

        # 使用data_utils中的特征提取函数
        features_df = extract_features(df, window_size=window_size, features=features)

        # 存储特征提取配置
        self.feature_extractors[dataset_name] = {
            "window_size": window_size,
            "features": features,
            "extracted_at": datetime.now().isoformat(),
            "source_columns": df.select_dtypes(include=[np.number]).columns.tolist(),
            "feature_columns": features_df.columns.tolist(),
        }

        return features_df

    def standardize_dataset(
        self,
        df: pd.DataFrame,
        dataset_name: str,
        method: str = "zscore",
        columns: Optional[List[str]] = None,
        feature_range: Tuple[float, float] = (-1, 1),
    ) -> pd.DataFrame:
        """
        标准化数据集

        参数:
        -----
        df : pd.DataFrame
            要标准化的数据框
        dataset_name : str
            数据集名称，用于存储缩放器
        method : str, 可选
            标准化方法，可选'zscore', 'minmax'
        columns : List[str], 可选
            要标准化的列，如果为None则标准化所有数值列
        feature_range : Tuple[float, float], 可选
            MinMax缩放的目标范围，默认为(-1, 1)

        返回:
        -----
        pd.DataFrame
            标准化后的数据框
        """
        self.logger.info(f"标准化数据集 {dataset_name}，方法={method}")

        # 创建新的缩放器
        if method == "zscore":
            scaler = StandardScaler()
        elif method == "minmax":
            scaler = MinMaxScaler(feature_range=feature_range)
        else:
            raise ValueError(f"不支持的标准化方法: {method}")

        # 使用data_utils中的标准化函数
        standardized_df, fitted_scaler = standardize_data(
            df,
            method=method,
            columns=columns,
            scaler=scaler,
            feature_range=feature_range,
        )

        # 存储缩放器
        self.scalers[dataset_name] = {
            "scaler": fitted_scaler,
            "method": method,
            "columns": (
                columns
                if columns is not None
                else df.select_dtypes(include=[np.number]).columns.tolist()
            ),
            "fitted_at": datetime.now().isoformat(),
        }

        # 存储标准化后的数据集
        self.data[dataset_name] = standardized_df

        return standardized_df

    def split_dataset(
        self,
        df: pd.DataFrame,
        dataset_name: str,
        target_col: Optional[str] = None,
        test_size: Optional[float] = None,
        val_size: Optional[float] = None,
        shuffle: Optional[bool] = None,
        random_state: Optional[int] = None,
    ) -> Dict[str, Union[pd.DataFrame, Dict]]:
        """
        将数据集分割为训练集、验证集和测试集

        参数:
        -----
        df : pd.DataFrame
            要分割的数据框
        dataset_name : str
            数据集名称
        target_col : str, 可选
            目标列名，如果提供则将数据拆分为X和y
        test_size : float, 可选
            测试集比例，如果为None则使用配置中的值
        val_size : float, 可选
            验证集比例，如果为None则使用配置中的值
        shuffle : bool, 可选
            是否打乱数据，如果为None则使用配置中的值
        random_state : int, 可选
            随机种子，如果为None则使用配置中的值

        返回:
        -----
        Dict
            包含分割好的数据集
        """
        # 使用配置中的默认值
        split_config = self.config.get("data_split", {})
        if test_size is None:
            test_size = split_config.get("test_size", 0.2)
        if val_size is None:
            val_size = split_config.get("val_size", 0.1)
        if shuffle is None:
            shuffle = split_config.get("shuffle", True)
        if random_state is None:
            random_state = split_config.get("random_state", 42)

        self.logger.info(
            f"分割数据集 {dataset_name}，"
            f"测试集比例={test_size}，"
            f"验证集比例={val_size}，"
            f"shuffle={shuffle}"
        )

        # 使用data_utils中的数据分割函数
        split_data_result = split_data(
            df,
            target_col=target_col,
            test_size=test_size,
            val_size=val_size,
            shuffle=shuffle,
            random_state=random_state,
        )

        # 记录分割信息
        split_info = {
            "target_col": target_col,
            "test_size": test_size,
            "val_size": val_size,
            "shuffle": shuffle,
            "random_state": random_state,
            "split_at": datetime.now().isoformat(),
        }

        # 根据是否有目标列，记录不同的分割结果形状
        if target_col is not None and "X_train" in split_data_result:
            # 有目标列的情况
            split_info.update(
                {
                    "X_train_shape": split_data_result["X_train"].shape,
                    "X_val_shape": split_data_result["X_val"].shape,
                    "X_test_shape": split_data_result["X_test"].shape,
                }
            )
        else:
            # 无目标列的情况
            split_info.update(
                {
                    "train_shape": split_data_result["train"].shape,
                    "val_shape": split_data_result["val"].shape,
                    "test_shape": split_data_result["test"].shape,
                }
            )

        # 存储分割结果
        self.data[f"{dataset_name}_split"] = {
            "data": split_data_result,
            "info": split_info,
        }

        return {"data": split_data_result, "info": split_info}

    def save_dataset(
        self, name: str, file_path: str, format: str = "csv", index: bool = False
    ) -> None:
        """
        保存数据集到文件

        参数:
        -----
        name : str
            数据集名称
        file_path : str
            保存路径
        format : str, 可选
            保存格式，可选'csv', 'json', 'hdf', 'parquet', 'excel'
        index : bool, 可选
            是否保存索引，默认为False
        """
        if name not in self.data:
            self.logger.error(f"数据集 {name} 不存在")
            raise KeyError(f"数据集 {name} 不存在")

        df = self.data[name]

        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)

        self.logger.info(f"保存数据集 {name} 到 {file_path}，格式={format}")

        try:
            if format == "csv":
                df.to_csv(file_path, index=index)
            elif format == "json":
                df.to_json(file_path, orient="records")
            elif format in ["h5", "hdf", "hdf5"]:
                df.to_hdf(file_path, key="data", mode="w")
            elif format == "parquet":
                df.to_parquet(file_path, index=index)
            elif format in ["excel", "xlsx", "xls"]:
                df.to_excel(file_path, index=index)
            else:
                raise ValueError(f"不支持的文件格式: {format}")

            self.logger.info(f"数据集 {name} 已保存")
        except Exception as e:
            self.logger.error(f"保存数据集 {name} 失败: {str(e)}")
            raise

    def save_metadata(self, file_path: str) -> None:
        """
        保存元数据到JSON文件

        参数:
        -----
        file_path : str
            保存路径
        """
        # 更新最后修改时间
        self.metadata["last_updated"] = datetime.now().isoformat()

        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)

        # 将整个元数据字典转换为JSON可序列化的格式
        serializable_metadata = self._make_json_serializable(self.metadata)

        try:
            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(serializable_metadata, f, ensure_ascii=False, indent=2)
            self.logger.info(f"元数据已保存到 {file_path}")
        except Exception as e:
            self.logger.error(f"保存元数据失败: {str(e)}")
            raise

    def _make_json_serializable(self, obj):
        """
        将对象转换为JSON可序列化的类型

        参数:
        -----
        obj : Any
            要转换的对象

        返回:
        -----
        Any
            转换后的对象
        """
        if isinstance(obj, (int, float, str, bool, type(None))):
            return obj
        elif isinstance(obj, (np.integer, np.int64)):
            return int(obj)
        elif isinstance(obj, (np.floating, np.float64, np.float32)):
            return float(obj)
        elif isinstance(obj, (np.ndarray, list, tuple)):
            return [self._make_json_serializable(x) for x in obj]
        elif isinstance(obj, dict):
            return {k: self._make_json_serializable(v) for k, v in obj.items()}
        elif hasattr(obj, "tolist"):
            return obj.tolist()
        elif hasattr(obj, "__dict__"):
            return {k: self._make_json_serializable(v) for k, v in obj.__dict__.items()}
        else:
            return str(obj)

    def get_dataset(self, name: str) -> pd.DataFrame:
        """
        获取数据集

        参数:
        -----
        name : str
            数据集名称

        返回:
        -----
        pd.DataFrame
            数据集
        """
        if name not in self.data:
            self.logger.error(f"数据集 {name} 不存在")
            raise KeyError(f"数据集 {name} 不存在")

        return self.data[name]

    def get_scaler(self, name: str) -> Dict:
        """
        获取缩放器

        参数:
        -----
        name : str
            数据集名称

        返回:
        -----
        Dict
            缩放器信息
        """
        if name not in self.scalers:
            self.logger.error(f"数据集 {name} 的缩放器不存在")
            raise KeyError(f"数据集 {name} 的缩放器不存在")

        return self.scalers[name]

    def get_feature_extractor(self, name: str) -> Dict:
        """
        获取特征提取器

        参数:
        -----
        name : str
            数据集名称

        返回:
        -----
        Dict
            特征提取器信息
        """
        if name not in self.feature_extractors:
            self.logger.error(f"数据集 {name} 的特征提取器不存在")
            raise KeyError(f"数据集 {name} 的特征提取器不存在")

        return self.feature_extractors[name]

    def get_metadata(self) -> Dict:
        """
        获取元数据

        返回:
        -----
        Dict
            元数据
        """
        return self.metadata
