import torch
import torch.nn as nn  # 虽然不直接用，但architecture可能会依赖
import pandas as pd
import numpy as np
from sklearn.preprocessing import MinMaxScaler
import os

# 从新的架构文件中导入模型类
try:
    from .lstm_kan_architecture import LSTMKATAutoencoder
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    try:
        from lstm_kan_architecture import LSTMKATAutoencoder
    except ImportError:
        # 如果都失败，定义一个占位符类
        print("警告: 无法导入LSTMKATAutoencoder，异常检测功能将受限")
        LSTMKATAutoencoder = None

# 假设您的LSTM+KAN模型定义在一个名为 lstm_kan_model.py (或类似) 的文件中
# from .lstm_kan_model import LSTMKAutoencoder # 这是一个占位符，您需要替换为实际的模型类

# 占位符：如果模型类直接在 .pth 文件中通过 torch.load 加载，并且不需要单独的 .py 定义文件
# 那么您可能需要调整加载方式。


class AnomalyModel:
    """
    异常工况模型，用于集成LSTM+KAT检测结果并参数化模拟异常工况。
    """

    def __init__(
        self,
        pv_digital_twin_model,
        model_path=None,
        model_params=None,
        feature_columns=None,
        initial_threshold=200.0,
    ):
        """
        初始化异常模型。

        参数:
        -----
        pv_digital_twin_model : PVDigitalTwin
            关联的PVDigitalTwin实例，用于应用异常效应。
        model_path : str, 可选
            预训练的LSTM+KAN模型的.pth文件路径。如果为None，则不加载PyTorch模型。
        model_params : dict, 可选
            实例化LSTMKATAutoencoder所需的参数。
        feature_columns : list, 可选
            模型训练时使用的特征列名列表。
        initial_threshold : float, 可选
            初始的异常检测MAE阈值。
        """
        self.pv_twin = pv_digital_twin_model
        self.model_path = model_path
        self.threshold = initial_threshold
        self.detected_anomalies_log = []
        self.max_log_size = 200  # 最大日志条数

        if model_params is None:
            self.model_params = {
                "input_dim": 4,
                "hidden_dim": 16,
                "latent_dim": 4,
                "nhead": 2,
                "num_layers": 1,
                "kan_groups": 4,
            }
        else:
            self.model_params = model_params

        if feature_columns is None:
            self.feature_columns = [
                "AC_POWER",
                "AMBIENT_TEMPERATURE",
                "MODULE_TEMPERATURE",
                "IRRADIATION",
            ]
        else:
            self.feature_columns = feature_columns

        # 确保 input_dim 与特征列数量一致
        if "input_dim" in self.model_params and self.model_params["input_dim"] != len(
            self.feature_columns
        ):
            print(
                f"警告: model_params中的input_dim ({self.model_params['input_dim']}) 与 feature_columns数量 ({len(self.feature_columns)}) 不匹配。将使用feature_columns的数量更新input_dim。"
            )
            self.model_params["input_dim"] = len(self.feature_columns)

        # 自动查找模型路径
        if not self.model_path:
            self.model_path = self._find_model_path()

        self.detection_model = self._load_detection_model(self.model_path)
        self.scaler = MinMaxScaler()  # 初始化scaler
        self.is_scaler_fitted = False

        self.anomaly_effect_mappers = {
            "RECONSTRUCTION_FAULT": {  # 通用故障类型
                "method_name": "apply_simulated_performance_loss",
                "param_name": "loss_factor",  # 修正参数名以匹配PVDigitalTwin中的方法
                "severity_scaling": 0.5,  # 示例：性能损失 = 严重程度 * severity_scaling
            },
            "HOTSPOT": {  # 保留原有的，如果将来模型能区分，或者手动指定
                "method_name": "apply_hotspot_effect",
                "default_params": {
                    "affected_cells_ratio": 0.1,
                    "performance_reduction_factor": 0.2,
                },
            },
            "DEGRADATION": {
                "method_name": "apply_degradation",
                "default_params": {"years_passed": 5, "annual_degradation_rate": 0.01},
            },
        }

    def _find_model_path(self):
        """
        自动查找LSTM+KAN模型文件路径

        Returns:
            str or None: 找到的模型文件路径，如果未找到则返回None
        """
        # 可能的模型文件路径列表
        possible_paths = [
            # detect目录中的模型文件
            os.path.join(os.path.dirname(__file__), '..', '..', 'detect', 'lstm_kat_autoencoder.pth'),
            # models目录中的模型文件
            os.path.join(os.path.dirname(__file__), '..', '..', 'models', 'lstm_kat_autoencoder.pth'),
            # 当前目录中的模型文件
            os.path.join(os.path.dirname(__file__), 'lstm_kat_autoencoder.pth'),
        ]

        for path in possible_paths:
            abs_path = os.path.abspath(path)
            if os.path.exists(abs_path):
                print(f"找到LSTM+KAN模型文件: {abs_path}")
                return abs_path

        print("警告: 未找到LSTM+KAN模型文件，异常检测功能将不可用")
        return None

    def _load_detection_model(self, model_path):
        if LSTMKATAutoencoder is None:
            print("警告: LSTMKATAutoencoder类未可用，无法加载异常检测模型")
            return None

        if model_path is None or not os.path.exists(model_path):
            print(
                f"警告: 异常检测模型路径未提供或文件不存在 ({model_path})。检测模型将不会被加载。"
            )
            return None
        try:
            # 实例化模型
            model = LSTMKATAutoencoder(**self.model_params)
            # 加载state_dict
            model.load_state_dict(
                torch.load(model_path, map_location=torch.device("cpu"))
            )
            model.eval()  # 设置为评估模式
            print(
                f"LSTMKATAutoencoder模型从 {model_path} 加载成功。参数: {self.model_params}"
            )
            return model
        except Exception as e:
            print(f"错误：加载LSTMKATAutoencoder模型失败: {e}")
            print(
                f"请确保模型路径 {model_path} 正确，模型参数 {self.model_params} 与保存的模型匹配。"
            )
            return None

    def _preprocess_data(self, input_df_single_step):
        """
        对输入的单时间步DataFrame进行预处理：特征选择、缩放、重塑、转换为Tensor。
        Args:
            input_df_single_step (pd.DataFrame): 包含单时间步特征数据的DataFrame。
                                                 必须包含self.feature_columns中定义的列。
        """
        if not all(col in input_df_single_step.columns for col in self.feature_columns):
            missing_cols = [
                col
                for col in self.feature_columns
                if col not in input_df_single_step.columns
            ]
            # Allow graceful continuation if some features are missing, by filling with NaN or 0 for scaling
            # This might not be ideal for model performance but prevents crashing.
            print(
                f"警告: 输入数据缺少必要的特征列: {missing_cols}. 将尝试使用NaN填充进行预处理。"
            )
            for col in missing_cols:
                input_df_single_step[col] = (
                    np.nan
                )  # Or 0, depending on how scaler handles NaN

        data_selected = input_df_single_step[self.feature_columns].copy()

        # Handle potential NaNs before scaling, e.g., by filling with mean of column if available or 0
        # This is a simplistic approach for robustness; better imputation might be needed.
        for col in data_selected.columns:
            if data_selected[col].isnull().any():
                # print(f"警告: 特征 '{col}' 包含NaN值。将用0填充。")
                data_selected[col] = data_selected[col].fillna(0)  # Simple fill with 0

        # 数据标准化 (MinMaxScaler)
        if not self.is_scaler_fitted:  # 简单处理：只在第一次调用时拟合
            data_scaled = self.scaler.fit_transform(data_selected)
            self.is_scaler_fitted = True
            print("Scaler已在当前数据上拟合。建议使用在代表性正常数据上拟合的scaler。")
        else:
            data_scaled = self.scaler.transform(data_selected)

        # 重塑为 (batch_size, sequence_length=1, num_features)
        # sequence_length = 1 因为模型说明中提到每个时间点独立处理
        data_reshaped = data_scaled.reshape(
            data_scaled.shape[0],
            1,
            data_scaled.shape[1],  # Should be (1, 1, num_features) for single step
        )

        # 转换为PyTorch张量
        data_tensor = torch.FloatTensor(data_reshaped)
        return data_tensor, data_selected  # 返回原始选择的数据用于计算MAE时的AC_POWER

    def detect_and_apply_anomaly(self, current_pv_state_dict, weather_for_step_dict):
        """
        使用LSTM+KAN模型检测异常，并根据检测结果在PVDigitalTwin中应用相应的效应。
        由SimulationEngine在每个时间步调用。

        参数:
        -----
        current_pv_state_dict : dict
            当前PV系统的状态，由 PVDigitalTwin.get_current_state_for_anomaly_detection() 提供。
            预计包含 'ac_power', 'temp_cell' 等，以及用于模型输入的 'ghi' (effective_irradiance), 'temp_air'。
            Datetime should also be present.
        weather_for_step_dict : dict
            当前时间步的天气数据 (可能与pv_state_dict中的天气信息有重叠，确保一致性)。

        返回:
        -----
        dict or None
            包含检测到的异常信息和应用结果的字典，如果无异常或无模型则返回None。
        """
        if self.detection_model is None:
            # print("无异常检测模型加载，跳过检测。")
            return None

        # 准备模型输入数据 (单行DataFrame)
        # 特征名基于 self.feature_columns, e.g., ['AC_POWER', 'AMBIENT_TEMPERATURE', 'MODULE_TEMPERATURE', 'IRRADIATION']
        # Map PVDigitalTwin output keys to AnomalyModel feature_columns names
        # Example mapping (adjust based on actual keys in PVDigitalTwin.get_current_state_for_anomaly_detection)
        # and self.feature_columns

        model_input_data = {}
        model_input_data["AC_POWER"] = current_pv_state_dict.get(
            "ac_power"
        )  # kW from pv_twin
        # LSTM+KAN model was trained on AC_POWER in Watts. If pv_twin returns kW, convert it.
        # Assuming the training notebook used Watts.
        if model_input_data["AC_POWER"] is not None:
            model_input_data[
                "AC_POWER"
            ] *= 1000  # Convert kW to W for consistency with training

        model_input_data["AMBIENT_TEMPERATURE"] = current_pv_state_dict.get("temp_air")
        model_input_data["MODULE_TEMPERATURE"] = current_pv_state_dict.get("temp_cell")
        model_input_data["IRRADIATION"] = current_pv_state_dict.get(
            "effective_irradiance", current_pv_state_dict.get("ghi")
        )

        current_datetime = current_pv_state_dict.get("datetime", pd.Timestamp.now())

        # Check for None values which might break the model or scaler
        for key, value in model_input_data.items():
            if value is None:
                print(
                    f"警告: Anomaly detection input feature '{key}' is None for timestamp {current_datetime}. Using 0."
                )
                model_input_data[key] = (
                    0  # Replace None with 0, or handle more gracefully
                )

        input_df = pd.DataFrame([model_input_data], index=[current_datetime])
        input_df.index.name = "datetime"

        try:
            input_tensor, original_selected_data = self._preprocess_data(input_df)
        except ValueError as e:
            print(f"数据预处理失败 @ {current_datetime}: {e}")
            return {
                "error": f"Data preprocessing failed: {e}",
                "timestamp": current_datetime,
            }

        try:
            with torch.no_grad():
                reconstructed_tensor = self.detection_model(input_tensor)
        except Exception as e:
            print(f"LSTM+KAN模型推断失败 @ {current_datetime}: {e}")
            return {
                "error": "Anomaly detection inference failed.",
                "details": str(e),
                "timestamp": current_datetime,
            }

        reconstructed_reshaped = reconstructed_tensor.numpy()[:, 0, :]
        reconstructed_original_scale = self.scaler.inverse_transform(
            reconstructed_reshaped
        )
        reconstructed_df = pd.DataFrame(
            reconstructed_original_scale,
            columns=self.feature_columns,
            index=original_selected_data.index,
        )

        anomaly_type, severity, actual_mae = self._interpret_prediction(
            original_selected_data, reconstructed_df, self.threshold
        )

        log_entry = {
            "timestamp": current_datetime,
            "input_features": model_input_data,
            "reconstructed_ac_power_watts": (
                reconstructed_df["AC_POWER"].iloc[0]
                if "AC_POWER" in reconstructed_df
                else None
            ),
            "actual_mae_ac_power": actual_mae,
            "threshold_mae": self.threshold,
            "anomaly_type": anomaly_type,
            "severity": severity,
            "applied_effect": None,
        }

        if anomaly_type and anomaly_type in self.anomaly_effect_mappers:
            mapper = self.anomaly_effect_mappers[anomaly_type]
            method_name = mapper["method_name"]
            params_to_apply = {}
            if "default_params" in mapper:
                params_to_apply = mapper["default_params"].copy()

            if "param_name" in mapper and "severity_scaling" in mapper:
                param_to_scale = mapper["param_name"]
                scaling_factor = mapper["severity_scaling"]
                # Ensure severity (0-1) is used for scaling loss factor (0-1)
                # Example: if severity is high (e.g., 0.8), and scaling is 0.5, actual loss factor is 0.4
                params_to_apply[param_to_scale] = min(
                    severity * scaling_factor, 1.0
                )  # Cap loss factor at 1.0
            elif (
                mapper.get("method_name") == "apply_simulated_performance_loss"
            ):  # Generic fault
                # Default behavior for RECONSTRUCTION_FAULT if no specific param_name/severity_scaling
                # Use a default scaling for severity to loss_factor
                params_to_apply["loss_factor"] = min(
                    severity * 0.5, 1.0
                )  # Example: severity maps to 50% of itself as loss

            try:
                if hasattr(self.pv_twin, method_name):
                    method_to_call = getattr(self.pv_twin, method_name)
                    # Pass effect_details for better logging in PVDigitalTwin
                    effect_details_for_pv_twin = {
                        "type": anomaly_type,
                        "severity": severity,
                        "mae": actual_mae,
                        "timestamp": current_datetime,
                    }
                    params_to_apply["effect_details"] = effect_details_for_pv_twin

                    method_to_call(**params_to_apply)
                    log_entry["applied_effect"] = {
                        "method": method_name,
                        "params": params_to_apply,
                    }
                    print(
                        f"成功将 {anomaly_type} (MAE: {actual_mae:.2f}, Severity: {severity:.2f}) 效应应用于PVDigitalTwin @ {current_datetime}。参数: {params_to_apply}"
                    )
                else:
                    print(f"错误: PVDigitalTwin没有方法 {method_name}")
                    log_entry["applied_effect"] = {
                        "error": f"Method {method_name} not found in PVDigitalTwin"
                    }

            except Exception as e:
                print(f"应用异常效应 {method_name} 到PVDigitalTwin失败: {e}")
                log_entry["applied_effect"] = {
                    "error": f"Failed to apply {method_name}: {str(e)}"
                }

        self.detected_anomalies_log.append(log_entry)

        # 限制日志大小，避免内存无限增长
        if len(self.detected_anomalies_log) > self.max_log_size:
            self.detected_anomalies_log = self.detected_anomalies_log[-self.max_log_size:]

        return log_entry

    def _interpret_prediction(
        self, original_data_df, reconstructed_data_df, threshold_mae
    ):
        """
        解释模型的重构输出以确定异常类型和严重程度。
        基于AC_POWER的MAE。

        参数:
        -----
        original_data_df : pd.DataFrame
            原始输入数据 (特征选择后，但未缩放)。
        reconstructed_data_df : pd.DataFrame
            模型重构的数据 (已逆变换回原始尺度)。
        threshold_mae : float
            AC_POWER MAE的异常阈值。

        返回:
        -----
        tuple : (str or None, float, float)
            (异常类型, 严重程度, 计算得到的MAE值)
            如果未检测到异常，则返回 (None, 0, mae)。
        """
        if (
            "AC_POWER" not in original_data_df.columns
            or "AC_POWER" not in reconstructed_data_df.columns
        ):
            raise ValueError(
                "AC_POWER column missing in original or reconstructed data for MAE calculation."
            )

        # 计算 AC_POWER 的 MAE (在整个序列/批次上平均)
        loss_mae_ac_power = (
            (original_data_df["AC_POWER"] - reconstructed_data_df["AC_POWER"])
            .abs()
            .mean()
        )

        if loss_mae_ac_power > threshold_mae:
            # 确保严重性在0-1之间
            # 例如，如果MAE远超阈值，严重性接近1；如果MAE略超阈值，严重性接近0。
            # 一个简单的线性映射，或者更复杂的非线性映射都可以
            max_expected_mae_above_threshold = (
                threshold_mae * 2
            )  # Example: if MAE is 3*threshold, severity is 1
            severity = min(
                max(
                    (loss_mae_ac_power - threshold_mae)
                    / max_expected_mae_above_threshold,
                    0.0,
                ),
                1.0,
            )
            return "RECONSTRUCTION_FAULT", severity, loss_mae_ac_power
        else:
            return None, 0, loss_mae_ac_power

    def add_anomaly_mapper(
        self,
        anomaly_type_id,
        pv_twin_method_name,
        default_params=None,
        param_name=None,
        severity_scaling=None,
    ):
        """
        添加或更新一个异常类型到PVDigitalTwin参数调整方法的映射。
        """
        if default_params is None:
            default_params = {}
        mapper_config = {
            "method_name": pv_twin_method_name,
            "default_params": default_params,
        }
        if param_name and severity_scaling is not None:
            mapper_config["param_name"] = param_name
            mapper_config["severity_scaling"] = severity_scaling

        self.anomaly_effect_mappers[anomaly_type_id] = mapper_config
        print(f"异常映射器已为 '{anomaly_type_id}' 添加/更新。")

    def set_threshold(self, new_threshold):
        """设置新的异常检测MAE阈值。"""
        self.threshold = new_threshold
        print(f"异常检测阈值已更新为: {self.threshold}")

    def get_detected_anomalies_summary(self):
        """返回检测到的异常日志的摘要。"""
        # 返回一个简化的摘要，或者完整的日志
        # For app.py, a list of dicts with key info is good
        summary = []
        for log_item in self.detected_anomalies_log:
            if log_item.get("anomaly_type"):  # Only include actual detected anomalies
                summary.append(
                    {
                        "timestamp": log_item.get("timestamp", "N/A"),
                        "type": log_item.get("anomaly_type", "N/A"),
                        "severity": log_item.get("severity", 0.0),
                        "mae": log_item.get("actual_mae_ac_power", 0.0),
                        "applied_effect": log_item.get("applied_effect"),
                    }
                )
        return summary

    def fit_scaler_on_reference_data(self, reference_df):
        """
        使用提供的参考DataFrame（应为正常运行数据）来拟合MinMaxScaler。
        Args:
            reference_df (pd.DataFrame): 包含正常运行数据的DataFrame，
                                         列应与self.feature_columns匹配。
        """
        if not all(col in reference_df.columns for col in self.feature_columns):
            missing_cols = [
                col for col in self.feature_columns if col not in reference_df.columns
            ]
            print(
                f"警告:Scaler参考数据缺少必要的特征列: {missing_cols}. Scaler未拟合。"
            )
            return

        data_to_fit = reference_df[self.feature_columns].copy()
        # Handle NaNs in reference data before fitting scaler
        for col in data_to_fit.columns:
            if data_to_fit[col].isnull().any():
                print(f"警告: Scaler参考数据特征 '{col}' 包含NaN值。将用0填充。")
                data_to_fit[col] = data_to_fit[col].fillna(0)

        self.scaler.fit(data_to_fit)
        self.is_scaler_fitted = True
        print(f"Scaler已在提供的参考数据上拟合。特征: {self.feature_columns}")


# 示例用法 (用于独立测试AnomalyModel)
if __name__ == "__main__":

    # 1. 创建一个Mock PVDigitalTwin实例 (或者导入并使用真实的PVDigitalTwin)
    class MockPVDigitalTwinForAnomaly:
        def __init__(self):
            self.name = "MockPVTwinForAnomaly"
            self.current_params = {"pdc0": 1000}
            print(f"{self.name} initialized.")

        def apply_simulated_performance_loss(
            self, loss_factor, effect_details=None
        ):
            loss = self.current_params["pdc0"] * loss_factor
            self.current_params["pdc0"] -= loss
            print(
                f"{self.name}: Applied performance loss of {loss_factor*100:.2f}%. Current pdc0: {self.current_params['pdc0']:.2f}. Details: {effect_details}"
            )

        def apply_hotspot_effect(
            self,
            affected_cells_ratio,
            performance_reduction_factor,
            effect_details=None,
        ):
            print(
                f"{self.name}: Hotspot applied. Affected: {affected_cells_ratio}, Reduction: {performance_reduction_factor}. Details: {effect_details}"
            )

        def apply_degradation(
            self, years_passed, annual_degradation_rate, effect_details=None
        ):
            print(
                f"{self.name}: Degradation applied. Years: {years_passed}, Rate: {annual_degradation_rate}. Details: {effect_details}"
            )

        def get_current_state_for_anomaly_detection(self, weather_for_step=None):
            # 返回用于异常检测的模拟数据
            return {
                "datetime": pd.Timestamp.now(),
                "ac_power": np.random.uniform(50, 800),  # Watts
                "temp_cell": np.random.uniform(20, 60),
                "temp_air": np.random.uniform(15, 40),
                "effective_irradiance": np.random.uniform(100, 1000),
                "ghi": np.random.uniform(100, 1000),
            }

    mock_pv_twin = MockPVDigitalTwinForAnomaly()

    # 2. 初始化AnomalyModel (假设有一个假的 .pth 文件路径或者模型路径为None)
    #    为了测试，我们可能需要一个实际的 dummy .pth 文件或在 _load_detection_model 中处理None路径
    dummy_model_path = "dummy_model.pth"  # Create a dummy file or ensure _load_detection_model handles its absence
    # Create a dummy model state_dict for LSTMKATAutoencoder if needed for testing without a real file
    # For now, assume model_path=None will prevent loading torch model, and detection will be skipped.

    anomaly_model = AnomalyModel(
        pv_digital_twin_model=mock_pv_twin,
        model_path=None,  # Test without a real model first
        initial_threshold=50.0,
    )
    print(
        f"AnomalyModel initialized. Detection model loaded: {anomaly_model.detection_model is not None}"
    )

    # 3. 准备一些模拟的当前PV状态和天气数据 (单时间步)
    current_pv_state = mock_pv_twin.get_current_state_for_anomaly_detection()
    weather_data_step = {
        "ghi": current_pv_state["ghi"],
        "temp_air": current_pv_state["temp_air"],
        "wind_speed": 2.0,
        "datetime": current_pv_state["datetime"],
    }

    print(f"\nSimulating anomaly detection for state: {current_pv_state}")

    # 4. 调用 detect_and_apply_anomaly
    # Since model is None, this should ideally not raise error but indicate no detection
    detection_result = anomaly_model.detect_and_apply_anomaly(
        current_pv_state, weather_data_step
    )
    print(f"Detection result (no model): {detection_result}")

    # 为scaler提供参考数据进行拟合 (可选，如果希望测试带scaler的预处理)
    # Create dummy reference data matching feature_columns
    num_reference_samples = 100
    reference_data_dict = {
        "AC_POWER": np.random.normal(
            loc=500, scale=100, size=num_reference_samples
        ),  # Watts
        "AMBIENT_TEMPERATURE": np.random.normal(
            loc=25, scale=5, size=num_reference_samples
        ),
        "MODULE_TEMPERATURE": np.random.normal(
            loc=45, scale=8, size=num_reference_samples
        ),
        "IRRADIATION": np.random.normal(loc=600, scale=200, size=num_reference_samples),
    }
    reference_df = pd.DataFrame(reference_data_dict)
    anomaly_model.fit_scaler_on_reference_data(reference_df)
    print(f"Scaler fitted: {anomaly_model.is_scaler_fitted}")

    # 再次调用检测，这次scaler应该已拟合
    # 如果有一个dummy的PyTorch模型和pth文件，这里的行为会更有意义
    detection_result_after_fit = anomaly_model.detect_and_apply_anomaly(
        current_pv_state, weather_data_step
    )
    print(f"Detection result (scaler fitted, no model): {detection_result_after_fit}")

    # 5. 检查PVDigitalTwin的状态是否被修改 (如果异常被模拟并应用)
    # (这部分取决于是否真的加载并运行了PyTorch模型并检测到异常)

    # 6. 获取异常日志
    summary = anomaly_model.get_detected_anomalies_summary()
    print(f"\nDetected anomalies summary: {summary}")

    # 测试 set_threshold
    anomaly_model.set_threshold(100.0)
    print(f"New threshold: {anomaly_model.threshold}")

    print("\nAnomalyModel test finished.")
