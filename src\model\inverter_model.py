import numpy as np
import pvlib
import pandas as pd


class InverterModel:
    """
    光伏逆变器模型类，用于模拟逆变器效率、MPPT算法和控制逻辑。
    """

    def __init__(self, parameters, mppt_algorithm="PO", po_step_size=0.01):
        """
        初始化逆变器模型。

        参数:
        ----------
        parameters : dict
            逆变器参数字典。应包含pvlib Sandia逆变器模型所需的参数，
            例如: Pac0, Pdc0, Vdc0, Ps0, C0, C1, C2, C3, Pnt。
            或者其他效率模型所需的参数。
        mppt_algorithm : str, 可选
            要使用的MPPT算法，例如 'PO' (Perturb & Observe), 'INC' (Incremental Conductance)。
            默认为 'PO'。
        po_step_size : float, 可选
            P&O算法的电压扰动步长（占Vdc0的百分比）或绝对电压值。默认为0.01 (1%)。
        """
        self.parameters = parameters
        self.mppt_algorithm = mppt_algorithm.upper()
        self.po_step_size_relative = po_step_size  # Store as relative for now
        self.v_mpp_previous = None
        self.p_mpp_previous = None
        self.perturb_direction = 1  # 1 for increasing voltage, -1 for decreasing

        # 检查必要的Sandia模型参数是否存在
        sandia_keys = ["Pac0", "Pdc0", "Vdc0", "Ps0", "C0", "C1", "C2", "C3", "Pnt"]
        missing_keys = [key for key in sandia_keys if key not in self.parameters]
        self.is_sandia_configured = len(missing_keys) == 0

        if not self.is_sandia_configured:
            print("警告: 未提供完整的Sandia逆变器参数。效率计算可能不准确或失败。")
            if missing_keys:
                print(f"  缺失的参数: {', '.join(missing_keys)}")
                print("  将使用简化的效率模型或固定效率值。")

                # 尝试从替代名称中填充
                aliases = {"Pac0": "Paco", "Pdc0": "Pdco", "Vdc0": "Vdco", "Ps0": "Pso"}

                for key in missing_keys:
                    if key in aliases and aliases[key] in self.parameters:
                        self.parameters[key] = self.parameters[aliases[key]]
                        print(f"  已从'{aliases[key]}'复制值到'{key}'")

            # 可以设置一个默认效率或简化的效率模型
            self.default_efficiency = self.parameters.get(
                "eta_inv_nom", 0.96
            )  # 从PVDigitalTwin的参数获取

    def get_efficiency(self, v_dc, p_dc):
        """
        根据直流输入电压和功率计算逆变器效率。
        优先使用Sandia模型，如果参数不完整则回退。

        参数:
        ----------
        v_dc : float or np.ndarray
            直流母线电压 (V)。
        p_dc : float or np.ndarray
            输入给逆变器的直流功率 (W)。

        返回:
        -------
        float or np.ndarray
            逆变器效率 (0到1之间)。
        """
        if p_dc <= 0:
            return 0.0

        if self.is_sandia_configured:
            # 使用 pvlib 的 Sandia 逆变器效率模型
            # 这个函数直接返回效率，而不是AC功率
            pac_out = pvlib.inverter.sandia(
                v_dc=v_dc,
                p_dc=p_dc,
                inverter=self.parameters,  # parameters 字典需要符合sandia函数的要求
            )
            # 避免除以零的情况
            efficiency = np.zeros_like(pac_out)
            valid_pdc = p_dc > 1e-6  # 避免非常小的p_dc导致数值问题
            if isinstance(p_dc, (np.ndarray, pd.Series)):
                efficiency[valid_pdc] = pac_out[valid_pdc] / p_dc[valid_pdc]
            elif valid_pdc:  # scalar case
                efficiency = pac_out / p_dc

            return np.nan_to_num(efficiency, nan=0.0, posinf=0.0, neginf=0.0)

        else:
            # 如果Sandia参数不完整，使用固定效率或简化的模型
            # print(f"使用默认效率: {self.default_efficiency}")
            return self.default_efficiency  # 或者实现一个基于输入功率的简单曲线

    def get_ac_power(self, v_dc, p_dc):
        """
        根据直流输入电压和功率以及逆变器效率计算交流输出功率。

        参数:
        ----------
        v_dc : float or np.ndarray
            直流母线电压 (V)。
        p_dc : float or np.ndarray
            输入给逆变器的直流功率 (W)。

        返回:
        -------
        float or np.ndarray
            交流输出功率 (W)。
        """
        if isinstance(p_dc, (np.ndarray, pd.Series)):
            if np.all(p_dc <= 0):
                return np.zeros_like(p_dc)
        elif p_dc <= 0:
            return 0.0

        efficiency = self.get_efficiency(v_dc, p_dc)
        ac_power = p_dc * efficiency

        # 应用额定功率限制
        pac0 = self.parameters.get("Pac0", float("inf"))
        return np.minimum(ac_power, pac0)

    def simulate_mppt_po_step(
        self,
        pv_system_interface,
        previous_v_mpp,
        previous_p_mpp,
        current_poa,
        current_temp_cell,
    ):
        """
        模拟P&O (Perturb and Observe) MPPT算法的一个步骤。

        参数:
        ----------
        pv_system_interface : object
            一个提供光伏系统I-V特性接口的对象。
            需要有一个方法 `get_power_at_voltage(voltage, poa, temp_cell)`
            或者 `get_iv_curve(poa, temp_cell)` 后自行查找。
            这里我们期望 `get_power_at_voltage`。
        previous_v_mpp : float
            上一个MPPT步骤的电压 (V)。
        previous_p_mpp : float
            上一个MPPT步骤的功率 (W)。
        current_poa : float
            当前平面总辐照度 (W/m^2)。
        current_temp_cell : float
            当前电池温度 (度C)。

        返回:
        -------
        next_v_mpp : float
            P&O算法确定的下一个工作电压 (V)。
        next_p_mpp : float
            在 next_v_mpp 下的功率 (W)。
        """
        if previous_v_mpp is None or previous_p_mpp is None:
            # 首次运行，从一个初始猜测开始，例如 Voc的某个比例或 Vdc0
            initial_v_guess = self.parameters.get(
                "Vdc0", 300
            )  # 使用逆变器额定直流电压作为初始点
            try:
                initial_v_guess = pv_system_interface.module_states[
                    "current_parameters"
                ].get("V_mp_ref", self.parameters.get("Vdc0", 300))
            except AttributeError:
                initial_v_guess = self.parameters.get("Vdc0", 300)

            current_v_mpp = initial_v_guess
            current_p_mpp = pv_system_interface.get_power_at_voltage(
                current_v_mpp, current_poa, current_temp_cell
            )

            self.v_mpp_previous = current_v_mpp
            self.p_mpp_previous = current_p_mpp
            self.perturb_direction = 1

            vdc0_for_step = self.parameters.get(
                "Vdc0", current_v_mpp if current_v_mpp > 1 else 300
            )
            actual_step_size = self.po_step_size_relative * vdc0_for_step

            next_v_mpp = current_v_mpp + self.perturb_direction * actual_step_size

        else:
            current_v_mpp = previous_v_mpp  # The voltage to be perturbed IS the previous MPP voltage
            # The current_p_mpp is power at THIS current_v_mpp (which is previous_v_mpp)
            # We then perturb current_v_mpp to get next_v_mpp, and find power at next_v_mpp

            # Power at the current operating point (which was the result of the last step)
            # This is equivalent to previous_p_mpp if conditions haven't changed, but we should use the new value
            # if we are in a loop where pv_system_interface.get_power_at_voltage uses current_poa, current_temp_cell
            # For P&O, we need power *before* perturbation and power *after* perturbation.
            # Let's assume previous_p_mpp is power at previous_v_mpp under *new* conditions.
            # This is slightly ambiguous in typical P&O descriptions if conditions change rapidly.
            # A more robust P&O takes two measurements for each decision.

            # Simple P&O: compare power at previous_v_mpp with power at (previous_v_mpp +/- step)
            vdc0_for_step = self.parameters.get(
                "Vdc0", previous_v_mpp if previous_v_mpp > 1 else 300
            )
            actual_step_size = self.po_step_size_relative * vdc0_for_step

            # Perturb voltage from the previous operating point
            perturbed_v = previous_v_mpp + self.perturb_direction * actual_step_size

            # Ensure perturbed voltage is within limits before measuring power
            v_min_mppt = self.parameters.get(
                "Vdc_min_mppt", 0.7 * self.parameters.get("Vdc0", 100)
            )
            v_max_mppt = self.parameters.get(
                "Vdc_max_mppt", 1.2 * self.parameters.get("Vdc0", 500)
            )
            perturbed_v = np.clip(perturbed_v, v_min_mppt, v_max_mppt)

            power_at_perturbed_v = pv_system_interface.get_power_at_voltage(
                perturbed_v, current_poa, current_temp_cell
            )

            delta_p = power_at_perturbed_v - previous_p_mpp

            if delta_p > 0:  # Power increased, perturbation was good
                next_v_mpp = perturbed_v
                # Keep same perturb_direction
            else:  # Power decreased or same, perturbation was bad, reverse direction for next time
                next_v_mpp = previous_v_mpp  # Go back to previous voltage or stay if no better found (can be previous_v_mpp or perturbed_v based on strategy)
                # For this simple P&O, if power decreases, we typically reverse and apply step from current_v_mpp
                # The next operating point *is* the perturbed point if it was better, otherwise it's previous_v_mpp + new_direction * step
                self.perturb_direction *= -1
                # If power decreased, the next actual operating point should be previous_v_mpp + new_direction*step
                # However, the 'next_v_mpp' for the *current* step, if delta_p <=0, means the best point was 'previous_v_mpp'
                # Let's re-evaluate the logic for 'next_v_mpp' when delta_p <= 0.
                # Standard P&O: if P_k > P_k-1, V_k+1 = V_k + dV (same direction). If P_k < P_k-1, V_k+1 = V_k - dV (reverse direction)
                # Here, previous_v_mpp is V_k-1, previous_p_mpp is P_k-1.
                # perturbed_v is V_k. power_at_perturbed_v is P_k.

                if delta_p > 0:  # P_k > P_k-1 (power_at_perturbed_v > previous_p_mpp)
                    next_v_mpp = perturbed_v  # V_k+1 = V_k (which is perturbed_v) + same_direction * step, but we are returning V_k as the current MPP.
                    # Let's clarify: function returns the *next operating point*.
                    # If current perturbation is good, next operating voltage is perturbed_v.
                    # self.perturb_direction remains the same for the *next* perturbation from next_v_mpp
                else:  # P_k <= P_k-1
                    next_v_mpp = previous_v_mpp  # Revert to V_k-1, and then perturb in opposite direction for *next* cycle.
                    self.perturb_direction *= (
                        -1
                    )  # Reverse direction for the *next* perturbation from next_v_mpp.

            # The actual voltage for the next P&O step's decision making.
            # If the perturbation (perturbed_v) resulted in higher power, then this becomes the new base for the next perturbation.
            # If not, the base remains previous_v_mpp, and the direction is changed.
            if power_at_perturbed_v > previous_p_mpp:
                operating_v_for_next_step = perturbed_v
                # perturb_direction is kept
            else:
                operating_v_for_next_step = (
                    previous_v_mpp  # Stay at previous V if perturbation was bad
                )
                self.perturb_direction *= -1  # Reverse for next time

            # The voltage to *return* as the MPPT point for this step:
            if (
                power_at_perturbed_v >= previous_p_mpp
            ):  # If power improved or stayed same (e.g. at MPP flat top)
                final_v_mpp_this_step = perturbed_v
            else:
                final_v_mpp_this_step = previous_v_mpp  # if power decreased, the previous point was better for this step

            next_v_mpp = final_v_mpp_this_step
            # The next voltage to *try* in the next P&O cycle:
            # next_v_to_actually_perturb_from = next_v_mpp
            # next_actual_perturbation = next_v_to_actually_perturb_from + self.perturb_direction * actual_step_size

        # Ensure voltage is within reasonable limits
        v_min_mppt = self.parameters.get(
            "Vdc_min_mppt", 0.7 * self.parameters.get("Vdc0", 100)
        )
        v_max_mppt = self.parameters.get(
            "Vdc_max_mppt", 1.2 * self.parameters.get("Vdc0", 500)
        )
        next_v_mpp = np.clip(next_v_mpp, v_min_mppt, v_max_mppt)

        next_p_mpp = pv_system_interface.get_power_at_voltage(
            next_v_mpp, current_poa, current_temp_cell
        )

        # Update history for the *next* P&O step
        self.v_mpp_previous = next_v_mpp
        self.p_mpp_previous = next_p_mpp

        return next_v_mpp, next_p_mpp

    def simulate_mppt_step(self, pv_system_interface, current_poa, current_temp_cell):
        """
        根据选择的MPPT算法模拟一个MPPT步骤。

        参数:
        ----------
        pv_system_interface : object
            光伏系统接口，需要提供 `get_power_at_voltage(voltage, poa, temp_cell)` 方法。
        current_poa : float
            当前平面总辐照度 (W/m^2)。
        current_temp_cell : float
            当前电池温度 (度C)。

        返回:
        -------
        v_mpp : float
            MPPT算法确定的工作电压 (V)。
        p_mpp : float
            在v_mpp下的光伏阵列输出功率 (W)。
        """
        if self.mppt_algorithm == "PO":
            v_mpp, p_mpp = self.simulate_mppt_po_step(
                pv_system_interface,
                self.v_mpp_previous,
                self.p_mpp_previous,
                current_poa,
                current_temp_cell,
            )
        elif self.mppt_algorithm == "IDEAL":
            iv_curve = pv_system_interface.get_iv_curve(
                effective_irradiance=current_poa, temperature_cell=current_temp_cell
            )
            if (
                iv_curve is not None
                and not iv_curve.empty
                and "p" in iv_curve.columns
                and not iv_curve["p"].empty
            ):
                mpp_idx = iv_curve["p"].idxmax()
                if pd.notna(mpp_idx):
                    mpp_point = iv_curve.loc[mpp_idx]
                    v_mpp = mpp_point["v"]
                    p_mpp = mpp_point["p"]
                    self.v_mpp_previous = v_mpp
                    self.p_mpp_previous = p_mpp
                else:
                    print("警告：无法在IV曲线中找到最大功率点(idxmax返回NaN)，返回None")
                    return None, None
            else:
                print("警告：无法获取有效IV曲线用于理想MPPT，返回None")
                return None, None
        else:
            raise NotImplementedError(f"MPPT算法 {self.mppt_algorithm} 尚未实现。")

        return v_mpp, p_mpp

    def reset_mppt_state(self):
        """重置MPPT算法的内部状态。"""
        self.v_mpp_previous = None
        self.p_mpp_previous = None
        self.perturb_direction = 1
        print("MPPT状态已重置。")

    # --- Placeholder methods for future development ---
    def get_control_state_machine(self):
        """
        （占位符）
        基于状态机模拟逆变器控制逻辑。
        返回当前状态或执行状态转换。
        """
        print("InverterModel: get_control_state_machine - 未实现")
        return {"state": "nominal", "details": "-"}

    def perform_grid_interaction(self, grid_conditions):
        """
        （占位符）
        模拟电网交互，如电压支持、频率响应。
        参数:
            grid_conditions (dict): 包含电网电压、频率等信息的字典。
        返回:
            dict: 控制动作或状态，例如调整的无功功率。
        """
        print(
            f"InverterModel: perform_grid_interaction with {grid_conditions} - 未实现"
        )
        return {"reactive_power_setpoint": 0, "active_power_setpoint": None}

    def apply_power_limit(self, limit_mw):
        """
        （占位符）
        应用有功功率限制（削减）。
        参数:
            limit_mw (float): 有功功率上限 (MW)。
        返回:
            float: 实际的有功功率设定点。
        """
        print(f"InverterModel: apply_power_limit to {limit_mw} MW - 未实现")
        return limit_mw * 1e6
