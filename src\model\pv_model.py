import pvlib
import pandas as pd
import numpy as np
from pvlib.pvsystem import PVSystem, retrieve_sam
from pvlib.location import Location
from pvlib.modelchain import ModelChain
from pvlib.temperature import TEMPERATURE_MODEL_PARAMETERS

# 导入单二极管和双二极管模型相关的函数
from pvlib.ivtools import sde

# 导入新的逆变器模型
from .inverter_model import InverterModel


class PVDigitalTwin:
    """光伏系统数字孪生模型类"""

    def __init__(
        self,
        latitude,
        longitude,
        altitude=0,
        name="PV System",
        tz="Asia/Shanghai",
        module_parameters=None,  # 改为None，后面会根据diode_model选择
        inverter_parameters=None,
        system_loss_dc_ohmic=0.02,  # Renamed for clarity, e.g., 2% DC ohmic loss
        diode_model="sde",  # 新增参数，可选 'sde' (单二极管) 或 'dde' (双二极管)
        temperature_model_parameters=None,  # 新增参数，用于热力学模型
        mppt_algorithm="IDEAL",  # Add MPPT algorithm choice for the new inverter model
        po_step_size=0.01,  # Add P&O step size if PO is chosen
        modules_per_string=10,  # New: PV modules in series per string
        strings_per_inverter=2,  # New: PV strings in parallel per inverter
        transformer_capacity_kva=50,  # New: kVA rating of the transformer
        transformer_efficiency=0.985,  # New: Efficiency of the transformer (e.g., 98.5%)
    ):
        """
        初始化光伏系统数字孪生模型

        参数:
        -----
        latitude : float
            系统位置纬度
        longitude : float
            系统位置经度
        altitude : float, 可选
            系统海拔高度，默认为0
        name : str, 可选
            系统名称，默认为\'PV System\'
        tz : str, 可选
            时区，默认为\'Asia/Shanghai\'
        module_parameters : dict, 可选
            光伏组件的物理参数，用于单二极管或双二极管模型。
            如果为None, 将尝试从SAM数据库加载标准组件。
        inverter_parameters : dict, 可选
            逆变器参数，如果为None则使用默认参数
        system_loss_dc_ohmic : float, 可选, 直流侧欧姆损耗百分比 (例如 0.02 for 2%). 默认为0.02.
        diode_model : str, 可选
            选择二极管模型，\'sde\' (单二极管) 或 \'dde\' (双二极管)，默认为 \'sde\'
        temperature_model_parameters : dict, 可选
            温度模型参数，例如 pvlib.temperature.TEMPERATURE_MODEL_PARAMETERS['sapm']['open_rack_glass_glass']
            如果为None，则使用默认的开架玻璃/玻璃参数。
        mppt_algorithm : str, 可选
            为自定义逆变器模型选择MPPT算法 ('PO', 'IDEAL', etc.). 默认为 'IDEAL' (理想MPPT).
        po_step_size : float, 可选
            如果 mppt_algorithm 为 'PO', 此参数定义P&O的电压扰动步长。
        modules_per_string : int, 可选, 每串中的组件数量. 默认为10.
        strings_per_inverter : int, 可选, 每逆变器连接的组串数量. 默认为2.
        transformer_capacity_kva : float, 可选, 变压器容量 (kVA). 默认为50.
        transformer_efficiency : float, 可选, 变压器效率 (0 to 1). 默认为0.985.
        """
        # 设置位置
        self.location = Location(
            latitude, longitude, tz=tz, altitude=altitude, name=name
        )

        # 组件参数处理
        if module_parameters is None:
            # 尝试从SAM加载一个示例组件参数
            # 注意：实际应用中应该允许用户传入更详细的组件参数
            try:
                sam_modules = retrieve_sam("cecmod")
                # 使用一个标准的CEC模块
                self.module_parameters = sam_modules[
                    "Canadian_Solar_Inc__CS6X_300M"
                ].copy()  # 使用copy()避免修改原始数据
                print(f"已从SAM数据库加载'Canadian_Solar_Inc__CS6X_300M'模块参数")
            except Exception as e:
                print(f"从SAM数据库加载模块参数失败: {e}")
                print("使用默认模块参数")
                # 创建一个基本的默认参数字典
                self.module_parameters = {
                    "I_L_ref": 8.5,  # 光电流参考值(A)
                    "I_o_ref": 1.0e-10,  # 暗电流参考值(A)
                    "R_s": 0.5,  # 串联电阻(ohm)
                    "R_sh_ref": 500,  # 并联电阻参考值(ohm)
                    "a_ref": 1.8,  # 理想因子参考值
                    "cells_in_series": 60,  # 串联电池数
                    "Technology": "mono-Si",  # 技术类型
                }
        else:
            self.module_parameters = (
                module_parameters.copy()
            )  # 使用copy()避免修改原始数据

        # 确保module_parameters包含PVWatts模型所需的参数
        # 对于PVWatts DC模型，必须有'pdc0'和'gamma_pdc'
        if "pdc0" not in self.module_parameters:
            print("添加缺失的'pdc0'参数到module_parameters...")
            # 从CEC模块参数中估算pdc0
            if "Pmp" in self.module_parameters:
                self.module_parameters["pdc0"] = float(self.module_parameters["Pmp"])
                print(f"  从'Pmp'={self.module_parameters['Pmp']}W设置'pdc0'")
            elif "Impp" in self.module_parameters and "Vmpp" in self.module_parameters:
                pdc0 = float(self.module_parameters["Impp"]) * float(
                    self.module_parameters["Vmpp"]
                )
                self.module_parameters["pdc0"] = pdc0
                print(f"  从Impp*Vmpp={pdc0:.2f}W设置'pdc0'")
            else:
                # 默认值：一个300W的模块
                self.module_parameters["pdc0"] = 300.0
                print(f"  使用默认值300.0W设置'pdc0'")
        else:
            # 确保pdc0是浮点数
            self.module_parameters["pdc0"] = float(self.module_parameters["pdc0"])
            print(f"模块'pdc0'已设置为{self.module_parameters['pdc0']}W")

        if "gamma_pdc" not in self.module_parameters:
            print("添加缺失的'gamma_pdc'参数到module_parameters...")
            # 从CEC模块参数中估算gamma_pdc
            if "gamma_pmp" in self.module_parameters:
                # gamma_pmp通常以%/°C表示，需要转换为1/°C
                self.module_parameters["gamma_pdc"] = (
                    float(self.module_parameters["gamma_pmp"]) / 100.0
                )
                print(
                    f"  从'gamma_pmp'={self.module_parameters['gamma_pmp']}%/°C设置'gamma_pdc'"
                )
            elif "beta_oc" in self.module_parameters:
                # 使用开路电压温度系数作为近似值
                # beta_oc通常以%/°C表示，需要转换为1/°C
                self.module_parameters["gamma_pdc"] = (
                    float(self.module_parameters["beta_oc"]) / 100.0
                )
                print(
                    f"  从'beta_oc'={self.module_parameters['beta_oc']}%/°C近似设置'gamma_pdc'"
                )
            else:
                # 默认值：-0.4%/°C
                self.module_parameters["gamma_pdc"] = -0.004
                print(f"  使用默认值-0.004(1/°C)设置'gamma_pdc'")
        else:
            # 确保gamma_pdc是浮点数
            self.module_parameters["gamma_pdc"] = float(
                self.module_parameters["gamma_pdc"]
            )
            print(f"模块'gamma_pdc'已设置为{self.module_parameters['gamma_pdc']}(1/°C)")

        self.diode_model = diode_model

        # 逆变器参数处理
        if inverter_parameters is None:
            try:
                sam_inverters = retrieve_sam("cecinverter")
                # 使用合适的逆变器型号
                self.inverter_parameters_dict = sam_inverters[
                    "ABB__MICRO_0_25_I_OUTD_US_208__208V_"
                ].copy()  # 使用copy()避免修改原始数据
                print(
                    f"已从SAM数据库加载'ABB__MICRO_0_25_I_OUTD_US_208__208V_'逆变器参数"
                )

                # 确保所有Sandia模型参数都存在
                sandia_keys = [
                    "Pac0",
                    "Pdc0",
                    "Vdc0",
                    "Ps0",
                    "C0",
                    "C1",
                    "C2",
                    "C3",
                    "Pnt",
                ]
                for key in sandia_keys:
                    if key not in self.inverter_parameters_dict:
                        if key == "Pac0" and "Paco" in self.inverter_parameters_dict:
                            self.inverter_parameters_dict["Pac0"] = (
                                self.inverter_parameters_dict["Paco"]
                            )
                        elif key == "Pdc0" and "Pdco" in self.inverter_parameters_dict:
                            self.inverter_parameters_dict["Pdc0"] = (
                                self.inverter_parameters_dict["Pdco"]
                            )
                        elif key == "Vdc0" and "Vdco" in self.inverter_parameters_dict:
                            self.inverter_parameters_dict["Vdc0"] = (
                                self.inverter_parameters_dict["Vdco"]
                            )
                        else:
                            # 设置默认值
                            if key in ["C0", "C1", "C2", "C3"]:
                                self.inverter_parameters_dict[key] = 0.0
                            elif key == "Ps0":
                                self.inverter_parameters_dict[key] = 1.0
                            elif key == "Pnt":
                                self.inverter_parameters_dict[key] = 0.1
                            print(f"  添加缺失的Sandia参数'{key}'，使用默认值")

            except Exception as e:
                print(f"从SAM数据库加载逆变器参数失败: {e}")
                print("使用默认逆变器参数")
                # 创建一个基本的默认参数字典，包含所有Sandia模型参数
                self.inverter_parameters_dict = {
                    "Paco": 250.0,  # AC额定功率(W)
                    "Pdco": 265.0,  # DC额定功率(W)
                    "Vdco": 40.0,  # DC额定电压(V)
                    "Pso": 1.0,  # 自耗电功率(W)
                    "C0": 0.0,  # 效率参数
                    "C1": 0.0,  # 效率参数
                    "C2": 0.0,  # 效率参数
                    "C3": 0.0,  # 效率参数
                    "Pnt": 0.1,  # 夜间损耗功率(W)
                    # 添加Sandia模型别名
                    "Pac0": 250.0,
                    "Pdc0": 265.0,
                    "Vdc0": 40.0,
                    "Ps0": 1.0,
                }
        else:
            self.inverter_parameters_dict = (
                inverter_parameters.copy()
            )  # 使用copy()避免修改原始数据

        # 确保inverter_parameters_dict包含PVWatts模型所需的参数
        # 对于PVWatts逆变器模型，必须有'pdc0'
        if "pdc0" not in self.inverter_parameters_dict:
            print("添加缺失的'pdc0'参数到inverter_parameters...")
            # 从CEC逆变器参数中估算pdc0
            if "Pdco" in self.inverter_parameters_dict:
                self.inverter_parameters_dict["pdc0"] = float(
                    self.inverter_parameters_dict["Pdco"]
                )
                print(
                    f"  从'Pdco'={self.inverter_parameters_dict['Pdco']}W设置逆变器'pdc0'"
                )
            elif "Paco" in self.inverter_parameters_dict:
                # 假设DC侧功率略大于AC侧
                pdc0 = float(self.inverter_parameters_dict["Paco"]) * 1.1
                self.inverter_parameters_dict["pdc0"] = pdc0
                print(f"  从'Paco'*1.1={pdc0:.2f}W设置逆变器'pdc0'")
            else:
                # 如果都没有，使用模块pdc0乘以模块数量
                total_modules = modules_per_string * strings_per_inverter
                module_pdc0 = self.module_parameters.get("pdc0", 300.0)
                pdc0 = total_modules * module_pdc0
                self.inverter_parameters_dict["pdc0"] = pdc0
                print(
                    f"  使用{total_modules}个模块×{module_pdc0}W={pdc0}W设置逆变器'pdc0'"
                )
        else:
            # 确保pdc0是浮点数
            self.inverter_parameters_dict["pdc0"] = float(
                self.inverter_parameters_dict["pdc0"]
            )
            print(f"逆变器'pdc0'已设置为{self.inverter_parameters_dict['pdc0']}W")

        # 创建自定义的InverterModel实例
        self.custom_inverter_model = InverterModel(
            parameters=self.inverter_parameters_dict,
            mppt_algorithm=mppt_algorithm,
            po_step_size=po_step_size,
        )

        # 系统损耗
        self.system_loss_dc_ohmic = system_loss_dc_ohmic
        self.transformer_capacity_kva = transformer_capacity_kva
        self.transformer_efficiency = transformer_efficiency

        # Convert kVA to W for transformer capacity, assuming power factor of 1 for simplicity
        # More advanced models could consider power factor.
        self.transformer_capacity_w = transformer_capacity_kva * 1000

        # 温度模型参数
        if temperature_model_parameters is None:
            self.temperature_model_parameters = TEMPERATURE_MODEL_PARAMETERS["sapm"][
                "open_rack_glass_glass"
            ]
        else:
            self.temperature_model_parameters = temperature_model_parameters

        # 创建PV系统对象
        # 注意：这里的module_parameters需要与所选的二极管模型兼容
        # pvlib的ModelChain在内部处理这些参数的传递
        self.system = PVSystem(
            module_parameters=self.module_parameters,
            inverter_parameters=self.inverter_parameters_dict,
            losses_parameters={"dc_ohmic_percent": system_loss_dc_ohmic * 100},
            temperature_model_parameters=self.temperature_model_parameters,
            modules_per_string=modules_per_string,
            strings_per_inverter=strings_per_inverter,
            surface_tilt=30,
            surface_azimuth=180,
        )

        # Store array configuration for later use (e.g. area calculation)
        self.modules_per_string = modules_per_string
        self.strings_per_inverter = strings_per_inverter
        self.total_modules = modules_per_string * strings_per_inverter

        # Store module area if available, for efficiency calculations
        # Common key for module area in SAM/CEC dbs is 'Area' or 'A_c' (m^2)
        self.module_area = self.module_parameters.get(
            "Area", self.module_parameters.get("A_c", 1.65)
        )  # Default to 1.65 m^2 if not found
        self.array_area = self.module_area * self.total_modules

        # Store loss parameters more directly if passed, for apply_settings
        self.loss_parameters = {
            "system_loss_dc_ohmic": system_loss_dc_ohmic,  # Percentage as input 0.02 for 2%
            "transformer_efficiency": transformer_efficiency,
            # For apply_settings callback compatibility
            "system_loss_input_percentage": system_loss_dc_ohmic * 100,
        }

        # 创建ModelChain对象用于仿真
        # 使用官方推荐的 with_pvwatts 类方法来确保正确的模型配置
        self._create_model_chain()

        # 初始化组件状态，用于热斑和老化等效应 (确保这在ModelChain创建后)
        self.module_states = {
            "initial_parameters": self.module_parameters.copy(),  # 保存原始参数
            "current_parameters": self.module_parameters.copy(),
            "degradation_rate": 0.005,  # 默认年衰减率 0.5%
            "hotspot_affected_cells": 0,  # 受热斑影响的电池单元数量或比例
            "cell_temperatures": None,  # 可以扩展为更详细的温度分布
        }

    def _create_model_chain(self):
        """创建ModelChain对象，确保使用正确的参数"""
        try:
            # 检查参数是否符合PVWatts模型的要求
            if "pdc0" not in self.module_parameters:
                raise ValueError("模块参数中缺少'pdc0'，这是PVWatts DC模型所需的")
            if "gamma_pdc" not in self.module_parameters:
                raise ValueError("模块参数中缺少'gamma_pdc'，这是PVWatts DC模型所需的")
            if "pdc0" not in self.inverter_parameters_dict:
                raise ValueError("逆变器参数中缺少'pdc0'，这是PVWatts逆变器模型所需的")

            # 方法1: 使用 with_pvwatts 类方法（推荐）
            self.model = ModelChain.with_pvwatts(
                self.system,
                self.location,
                name=self.location.name,
                ac_model="pvwatts",  # 显式指定AC模型
                aoi_model="physical",  # 使用物理入射角模型
                spectral_model="no_loss",  # 不考虑光谱损失
                temperature_model="sapm",  # 使用SAPM温度模型
            )
            print("ModelChain 在 __init__ 期间成功创建 (使用 with_pvwatts 方法)")
        except Exception as e:
            print(f"警告: with_pvwatts 方法失败，错误: {e}")
            print("尝试使用显式指定 dc_model='pvwatts' 的方法...")
            try:
                # 方法2: 显式指定 dc_model（备用方案）
                self.model = ModelChain(
                    self.system,
                    self.location,
                    dc_model="pvwatts",
                    ac_model="pvwatts",
                    losses_model="pvwatts",
                    name=self.location.name + "_explicit",
                )
                print(
                    "ModelChain 在 __init__ 期间成功创建 (显式指定 dc_model='pvwatts')"
                )
            except Exception as e_fallback:
                print(f"警告: 显式指定方法也失败，错误: {e_fallback}")
                print("尝试最简化的 ModelChain 配置...")
                try:
                    # 方法3: 最简化配置（最后备用方案）
                    self.model = ModelChain(
                        self.system,
                        self.location,
                        dc_model="pvwatts",
                        ac_model="pvwatts",
                        name=self.location.name + "_minimal",
                    )
                    print("ModelChain 在 __init__ 期间成功创建 (最简化配置)")
                except Exception as e_minimal:
                    print(f"致命错误: 所有ModelChain创建方法都失败: {e_minimal}")
                    # 确保在失败时 self.model 不会导致 AttributeError
                    self.model = None
                    raise RuntimeError(
                        f"无法在 __init__ 期间创建ModelChain: {e_minimal}"
                    ) from e_minimal

    def calculate_cell_temperature(self, poa_global, temp_air, wind_speed):
        """
        计算电池板温度。
        实际的ModelChain会在内部计算，这里提供一个外部调用的接口以便获取。
        也可以根据需要在此实现更复杂的温度分布模型。

        参数:
        -----
        poa_global : float or pd.Series
            平面总辐照度 (W/m^2)
        temp_air : float or pd.Series
            环境温度 (度C)
        wind_speed : float or pd.Series
            风速 (m/s)

        返回:
        -----
        float or pd.Series
            电池温度 (度C)
        """
        # 使用pvlib的温度模型进行计算
        # self.temperature_model_parameters 是在 __init__ 中定义的
        temp_cell = pvlib.temperature.sapm_cell(
            poa_global=poa_global,
            temp_air=temp_air,
            wind_speed=wind_speed,
            a=self.temperature_model_parameters["a"],
            b=self.temperature_model_parameters["b"],
            deltaT=self.temperature_model_parameters["deltaT"],
        )
        self.module_states["cell_temperatures"] = temp_cell  # 更新模块状态中的温度
        return temp_cell

    def apply_hotspot_effect(
        self,
        affected_cells_ratio=0.1,
        temperature_increase_factor=2.0,  # Currently not used to directly change temp, but by reducing performance
        performance_reduction_factor=0.2,
    ):
        """
        施加热斑效应。
        通过降低 pdc0 来模拟性能下降。
        """
        self.module_states["hotspot_affected_cells"] = affected_cells_ratio
        print(
            f"Applying hotspot: {affected_cells_ratio*100}% cells affected, reducing performance by {performance_reduction_factor*100}% of affected part."
        )

        # Effective loss factor on the entire module based on affected cell ratio and their perf reduction
        overall_loss_factor = affected_cells_ratio * performance_reduction_factor

        current_pdc0 = self.module_parameters.get(
            "pdc0", self.module_parameters.get("P_mp_ref")
        )
        if current_pdc0 is not None:
            new_pdc0 = current_pdc0 * (1 - overall_loss_factor)
            self.module_parameters["pdc0"] = new_pdc0
            if "P_mp_ref" in self.module_parameters:
                self.module_parameters["P_mp_ref"] = new_pdc0
            self.module_states["current_parameters"]["pdc0"] = new_pdc0
            if "P_mp_ref" in self.module_states["current_parameters"]:
                self.module_states["current_parameters"]["P_mp_ref"] = new_pdc0
            print(f"  Hotspot: Pdc0 changed from {current_pdc0:.2f} to {new_pdc0:.2f}")
            self._reinitialize_pvsystem_and_modelchain()
        else:
            print("Warning: Hotspot effect could not be applied, Pdc0 not found.")

    def apply_degradation(self, years_passed, annual_degradation_rate=None):
        """
        施加老化衰减效应。
        通过降低 pdc0 来模拟。
        """
        if annual_degradation_rate is None:
            annual_degradation_rate = self.module_states.get("degradation_rate", 0.005)

        # Calculate total degradation: (1 - rate)^years
        total_degradation_factor = (1 - annual_degradation_rate) ** years_passed
        print(
            f"Applying degradation for {years_passed} years with rate {annual_degradation_rate*100}%/year. Total factor: {total_degradation_factor:.4f}"
        )

        initial_pdc0 = self.module_states["initial_parameters"].get(
            "pdc0", self.module_states["initial_parameters"].get("P_mp_ref")
        )

        if initial_pdc0 is not None:
            new_pdc0 = initial_pdc0 * total_degradation_factor
            self.module_parameters["pdc0"] = new_pdc0
            if "P_mp_ref" in self.module_parameters:
                self.module_parameters["P_mp_ref"] = new_pdc0
            self.module_states["current_parameters"]["pdc0"] = new_pdc0
            if "P_mp_ref" in self.module_states["current_parameters"]:
                self.module_states["current_parameters"]["P_mp_ref"] = new_pdc0
            print(
                f"  Degradation: Pdc0 changed from {initial_pdc0:.2f} to {new_pdc0:.2f}"
            )
            self._reinitialize_pvsystem_and_modelchain()
        else:
            print("Warning: Degradation could not be applied, initial Pdc0 not found.")

    def reset_module_parameters(self):
        """
        将组件参数重置为初始状态，清除热斑和老化效应。
        """
        self.module_states["current_parameters"] = self.module_states[
            "initial_parameters"
        ].copy()
        self.module_states["hotspot_affected_cells"] = 0
        self.system.module_parameters = self.module_states["current_parameters"]
        self._reinitialize_pvsystem_and_modelchain()
        print("组件参数已重置为初始状态。")

    def _reinitialize_pvsystem_and_modelchain(self):
        """
        当系统参数（如组件、逆变器、损耗）发生变化时，重新初始化PVSystem和ModelChain。
        """
        print("重新初始化 PVSystem 和 ModelChain...")

        # 确保module_parameters包含PVWatts模型所需的关键参数
        if "pdc0" not in self.module_parameters:
            print("警告：module_parameters中缺少'pdc0'参数，这是PVWatts模型所必需的")
            # 尝试从其他参数中获取或使用默认值
            if "Pmp" in self.module_parameters:
                self.module_parameters["pdc0"] = float(self.module_parameters["Pmp"])
                print(f"已从'Pmp'设置'pdc0' = {self.module_parameters['pdc0']}W")
            elif "Impp" in self.module_parameters and "Vmpp" in self.module_parameters:
                pdc0 = float(self.module_parameters["Impp"]) * float(
                    self.module_parameters["Vmpp"]
                )
                self.module_parameters["pdc0"] = pdc0
                print(f"已从Impp*Vmpp计算'pdc0' = {pdc0:.2f}W")
            elif "P_max" in self.module_parameters:
                self.module_parameters["pdc0"] = float(self.module_parameters["P_max"])
                print(f"已从'P_max'设置'pdc0' = {self.module_parameters['pdc0']}W")
            else:
                # 使用默认值
                self.module_parameters["pdc0"] = 300.0  # 300W默认功率
                print(f"使用默认值设置'pdc0' = {self.module_parameters['pdc0']}W")
        else:
            # 确保pdc0是浮点数
            try:
                self.module_parameters["pdc0"] = float(self.module_parameters["pdc0"])
                print(f"已验证'pdc0' = {self.module_parameters['pdc0']}W")
            except (ValueError, TypeError):
                print(
                    f"警告：'pdc0'值 {self.module_parameters['pdc0']} 无法转换为浮点数，使用默认值"
                )
                self.module_parameters["pdc0"] = 300.0

        if "gamma_pdc" not in self.module_parameters:
            print(
                "警告：module_parameters中缺少'gamma_pdc'参数，这是PVWatts模型所必需的"
            )
            # 尝试从其他参数中获取或使用默认值
            if "gamma_pmp" in self.module_parameters:
                # gamma_pmp通常以%/°C表示，需要转换为1/°C
                try:
                    self.module_parameters["gamma_pdc"] = (
                        float(self.module_parameters["gamma_pmp"]) / 100.0
                    )
                    print(
                        f"已从'gamma_pmp'计算'gamma_pdc' = {self.module_parameters['gamma_pdc']}(1/°C)"
                    )
                except (ValueError, TypeError):
                    self.module_parameters["gamma_pdc"] = -0.004
                    print(
                        f"无法从'gamma_pmp'计算'gamma_pdc'，使用默认值 = {self.module_parameters['gamma_pdc']}(1/°C)"
                    )
            elif "beta_oc" in self.module_parameters:
                # 使用开路电压温度系数作为近似
                try:
                    self.module_parameters["gamma_pdc"] = (
                        float(self.module_parameters["beta_oc"]) / 100.0
                    )
                    print(
                        f"已从'beta_oc'近似设置'gamma_pdc' = {self.module_parameters['gamma_pdc']}(1/°C)"
                    )
                except (ValueError, TypeError):
                    self.module_parameters["gamma_pdc"] = -0.004
                    print(
                        f"无法从'beta_oc'计算'gamma_pdc'，使用默认值 = {self.module_parameters['gamma_pdc']}(1/°C)"
                    )
            else:
                # 使用默认值
                self.module_parameters["gamma_pdc"] = -0.004  # -0.4%/°C默认温度系数
                print(
                    f"使用默认值设置'gamma_pdc' = {self.module_parameters['gamma_pdc']}(1/°C)"
                )
        else:
            # 确保gamma_pdc是浮点数
            try:
                self.module_parameters["gamma_pdc"] = float(
                    self.module_parameters["gamma_pdc"]
                )
                print(
                    f"已验证'gamma_pdc' = {self.module_parameters['gamma_pdc']}(1/°C)"
                )
            except (ValueError, TypeError):
                print(
                    f"警告：'gamma_pdc'值 {self.module_parameters['gamma_pdc']} 无法转换为浮点数，使用默认值"
                )
                self.module_parameters["gamma_pdc"] = -0.004

        # 确保inverter_parameters包含PVWatts模型所需的关键参数
        if "pdc0" not in self.inverter_parameters_dict:
            print(
                "警告：inverter_parameters中缺少'pdc0'参数，这是PVWatts逆变器模型所必需的"
            )
            # 设置与module_parameters['pdc0']相同的值
            if "pdc0" in self.module_parameters:
                total_modules = self.modules_per_string * self.strings_per_inverter
                pdc0_total = float(self.module_parameters["pdc0"]) * total_modules
                self.inverter_parameters_dict["pdc0"] = pdc0_total
                print(
                    f"已设置逆变器'pdc0' = {pdc0_total:.2f}W (基于{total_modules}个模块)"
                )
            elif "Pdco" in self.inverter_parameters_dict:
                self.inverter_parameters_dict["pdc0"] = float(
                    self.inverter_parameters_dict["Pdco"]
                )
                print(
                    f"已从'Pdco'设置逆变器'pdc0' = {self.inverter_parameters_dict['pdc0']}W"
                )
            else:
                # 使用默认值
                self.inverter_parameters_dict["pdc0"] = 2200.0  # 2200W默认逆变器功率
                print(
                    f"使用默认值设置逆变器'pdc0' = {self.inverter_parameters_dict['pdc0']}W"
                )
        else:
            # 确保pdc0是浮点数
            try:
                self.inverter_parameters_dict["pdc0"] = float(
                    self.inverter_parameters_dict["pdc0"]
                )
                print(f"已验证逆变器'pdc0' = {self.inverter_parameters_dict['pdc0']}W")
            except (ValueError, TypeError):
                print(
                    f"警告：逆变器'pdc0'值 {self.inverter_parameters_dict['pdc0']} 无法转换为浮点数，使用默认值"
                )
                self.inverter_parameters_dict["pdc0"] = 2200.0

        # 重新创建PVSystem对象
        try:
            self.system = PVSystem(
                module_parameters=self.module_parameters,
                inverter_parameters=self.inverter_parameters_dict,  # 使用更新后的逆变器参数字典
                losses_parameters={
                    "dc_ohmic_percent": self.loss_parameters.get(
                        "system_loss_dc_ohmic", 0.02
                    )
                    * 100
                },
                temperature_model_parameters=self.temperature_model_parameters,
                modules_per_string=self.modules_per_string,
                strings_per_inverter=self.strings_per_inverter,
                surface_tilt=self.system.surface_tilt,  # 保留之前的倾角
                surface_azimuth=self.system.surface_azimuth,  # 保留之前的方位角
            )
            print("PVSystem 对象已重新创建。")
            print(
                f"模块参数: pdc0={self.module_parameters.get('pdc0')}W, gamma_pdc={self.module_parameters.get('gamma_pdc')}"
            )
            print(f"逆变器参数: pdc0={self.inverter_parameters_dict.get('pdc0')}W")
        except Exception as e:
            print(f"创建PVSystem对象失败: {e}")
            raise RuntimeError(f"重新初始化PVSystem失败: {e}")

        # 重新创建ModelChain对象
        # 使用与 __init__ 中相同的策略，优先使用 with_pvwatts 方法
        try:
            # 先检查参数是否符合PVWatts模型的要求
            if "pdc0" not in self.module_parameters:
                raise ValueError("模块参数中缺少'pdc0'，这是PVWatts DC模型所需的")
            if "gamma_pdc" not in self.module_parameters:
                raise ValueError("模块参数中缺少'gamma_pdc'，这是PVWatts DC模型所需的")
            if "pdc0" not in self.inverter_parameters_dict:
                raise ValueError("逆变器参数中缺少'pdc0'，这是PVWatts逆变器模型所需的")

            # 方法1: 使用 with_pvwatts 类方法（推荐）
            self.model = ModelChain.with_pvwatts(
                self.system,
                self.location,
                name=self.location.name + "_reinit",
                ac_model="pvwatts",  # 显式指定AC模型
                aoi_model="physical",  # 使用物理入射角模型
                spectral_model="no_loss",  # 不考虑光谱损失
                temperature_model="sapm",  # 使用SAPM温度模型
            )
            print("ModelChain 在重新初始化期间成功创建 (使用 with_pvwatts 方法)")

            # 创建一个用于测试的默认天气数据帧
            # 用于验证模型是否能够正常工作
            current_time = pd.Timestamp.now(tz=self.location.tz)
            weather_df = pd.DataFrame(
                {
                    "ghi": [1000],
                    "dni": [800],
                    "dhi": [200],
                    "temp_air": [25],
                    "wind_speed": [1],
                },
                index=pd.DatetimeIndex([current_time]),
            )

            # 运行一个简单的测试来验证模型是否正常工作
            try:
                self.model.run_model(weather=weather_df)
                print("ModelChain测试运行成功，模型初始化完成")
            except Exception as test_e:
                print(f"ModelChain测试运行失败: {test_e}，但模型已创建")

        except Exception as e:
            print(f"警告: with_pvwatts 方法失败，错误: {e}")
            print("尝试使用显式指定 dc_model='pvwatts' 的方法...")
            try:
                # 方法2: 显式指定 dc_model（备用方案）
                self.model = ModelChain(
                    self.system,
                    self.location,
                    dc_model="pvwatts",
                    ac_model="pvwatts",
                    losses_model="pvwatts",
                    name=self.location.name + "_reinit_explicit",
                )
                print(
                    "ModelChain 在重新初始化期间成功创建 (显式指定 dc_model='pvwatts')"
                )

                # 创建一个用于测试的默认天气数据帧
                current_time = pd.Timestamp.now(tz=self.location.tz)
                weather_df = pd.DataFrame(
                    {
                        "ghi": [1000],
                        "dni": [800],
                        "dhi": [200],
                        "temp_air": [25],
                        "wind_speed": [1],
                    },
                    index=pd.DatetimeIndex([current_time]),
                )

                try:
                    self.model.run_model(weather=weather_df)
                    print("ModelChain测试运行成功，模型初始化完成")
                except Exception as test_e:
                    print(f"ModelChain测试运行失败: {test_e}，但模型已创建")

            except Exception as e_fallback:
                print(f"警告: 显式指定方法也失败，错误: {e_fallback}")
                print("尝试最简化的 ModelChain 配置...")
                try:
                    # 方法3: 最简化配置（最后备用方案）
                    self.model = ModelChain(
                        self.system,
                        self.location,
                        dc_model="pvwatts",
                        ac_model="pvwatts",
                        name=self.location.name + "_reinit_minimal",
                    )
                    print("ModelChain 在重新初始化期间成功创建 (最简化配置)")

                    # 创建一个用于测试的默认天气数据帧
                    current_time = pd.Timestamp.now(tz=self.location.tz)
                    weather_df = pd.DataFrame(
                        {
                            "ghi": [1000],
                            "dni": [800],
                            "dhi": [200],
                            "temp_air": [25],
                            "wind_speed": [1],
                        },
                        index=pd.DatetimeIndex([current_time]),
                    )

                    try:
                        self.model.run_model(weather=weather_df)
                        print("ModelChain测试运行成功，模型初始化完成")
                    except Exception as test_e:
                        print(f"ModelChain测试运行失败: {test_e}，但模型已创建")

                except Exception as e_minimal:
                    print(f"致命错误: 所有ModelChain创建方法都失败: {e_minimal}")
                    # 确保在失败时 self.model 不会导致 AttributeError
                    self.model = None
                    raise RuntimeError(
                        f"无法在重新初始化期间创建ModelChain: {e_minimal}"
                    ) from e_minimal

    def apply_simulated_performance_loss(self, loss_factor, effect_details=None):
        """
        应用模拟性能损失到光伏模块参数。
        该方法直接修改self.module_parameters并重新初始化模型。

        Args:
            loss_factor (float): 要损失的性能比例（例如，0.1表示10%的损失）。
                                 这将减少pdc0（或等效功率额定值）。
            effect_details (dict, optional): 用于日志记录或更复杂效果的附加异常详情。
        """
        if not (0 <= loss_factor <= 1):
            print(
                f"警告: 无效的loss_factor {loss_factor}。必须在0到1之间。未应用损失。"
            )
            return

        print(f"开始应用性能损失: {loss_factor * 100:.2f}%")

        # 获取当前的直流功率额定值（例如，pdc0或P_mp_ref）
        # 如果多个损失可以累积，获取*当前*值很重要
        current_pdc0 = self.module_parameters.get(
            "pdc0", self.module_parameters.get("P_mp_ref", None)
        )
        current_I_L_ref = self.module_parameters.get("I_L_ref", None)

        # 获取当前逆变器pdc0
        current_inverter_pdc0 = self.inverter_parameters_dict.get("pdc0", None)

        applied_loss_info = f"应用损失因子: {loss_factor * 100:.2f}%"
        if effect_details and "type" in effect_details:
            applied_loss_info += f"，原因是 {effect_details['type']}"
        print(applied_loss_info)

        if current_pdc0 is not None:
            try:
                current_pdc0 = float(current_pdc0)
                new_pdc0 = current_pdc0 * (1.0 - loss_factor)

                # 更新模块pdc0参数
                self.module_parameters["pdc0"] = new_pdc0
                print(f"  模块pdc0从 {current_pdc0:.2f}W 变为 {new_pdc0:.2f}W")

                # 如果P_mp_ref是模型使用的主键，也更新它
                if "P_mp_ref" in self.module_parameters:
                    self.module_parameters["P_mp_ref"] = new_pdc0
                    print(f"  同步更新模块P_mp_ref为 {new_pdc0:.2f}W")

                # 更新逆变器的pdc0（根据PVWatts模型的要求）
                if current_inverter_pdc0 is not None:
                    try:
                        current_inverter_pdc0 = float(current_inverter_pdc0)
                        # 保持模块和逆变器的pdc0比例不变
                        new_inverter_pdc0 = current_inverter_pdc0 * (1.0 - loss_factor)
                        self.inverter_parameters_dict["pdc0"] = new_inverter_pdc0
                        print(
                            f"  逆变器pdc0从 {current_inverter_pdc0:.2f}W 变为 {new_inverter_pdc0:.2f}W"
                        )
                    except (ValueError, TypeError):
                        # 如果转换失败，使用模块pdc0的总和
                        total_modules = (
                            self.modules_per_string * self.strings_per_inverter
                        )
                        new_inverter_pdc0 = new_pdc0 * total_modules
                        self.inverter_parameters_dict["pdc0"] = new_inverter_pdc0
                        print(
                            f"  设置逆变器pdc0为 {new_inverter_pdc0:.2f}W（基于{total_modules}个模块）"
                        )
                else:
                    # 如果逆变器参数中没有pdc0，使用模块pdc0的总和
                    total_modules = self.modules_per_string * self.strings_per_inverter
                    new_inverter_pdc0 = new_pdc0 * total_modules
                    self.inverter_parameters_dict["pdc0"] = new_inverter_pdc0
                    print(
                        f"  设置逆变器pdc0为 {new_inverter_pdc0:.2f}W（基于{total_modules}个模块）"
                    )
            except (ValueError, TypeError) as e:
                print(f"错误: 无法处理pdc0值 '{current_pdc0}': {e}")
                # 继续使用I_L_ref或其他参数

        # 如果pdc0不可用或处理失败，尝试使用I_L_ref
        if (
            current_pdc0 is None
            or isinstance(current_pdc0, (str, bool))
            or (hasattr(current_pdc0, "size") and current_pdc0.size == 0)
        ):
            if current_I_L_ref is not None:
                try:
                    current_I_L_ref = float(current_I_L_ref)
                    new_I_L_ref = current_I_L_ref * (1.0 - loss_factor)
                    self.module_parameters["I_L_ref"] = new_I_L_ref
                    print(f"  I_L_ref从 {current_I_L_ref:.4f}A 变为 {new_I_L_ref:.4f}A")

                    # 确保pdc0参数存在于模块和逆变器参数中（PVWatts模型所需）
                    if "pdc0" not in self.module_parameters:
                        # 估算pdc0：假设每个模块300W
                        module_rated_power = 300.0  # 假设标准模块功率
                        estimated_pdc0 = module_rated_power * (1.0 - loss_factor)
                        self.module_parameters["pdc0"] = estimated_pdc0
                        print(f"  估算并设置模块pdc0为 {estimated_pdc0:.2f}W")

                        # 同时更新逆变器pdc0
                        total_modules = (
                            self.modules_per_string * self.strings_per_inverter
                        )
                        estimated_system_pdc0 = estimated_pdc0 * total_modules
                        self.inverter_parameters_dict["pdc0"] = estimated_system_pdc0
                        print(
                            f"  设置逆变器pdc0为 {estimated_system_pdc0:.2f}W（基于{total_modules}个模块）"
                        )
                except (ValueError, TypeError) as e:
                    print(f"错误: 无法处理I_L_ref值 '{current_I_L_ref}': {e}")
                    # 如果这也失败了，我们将尝试创建默认值
                    # 这里使用默认值，而不是抛出错误，以提高鲁棒性
                    self.module_parameters["pdc0"] = 300.0 * (1.0 - loss_factor)
                    print(
                        f"  使用默认值设置模块pdc0为 {self.module_parameters['pdc0']:.2f}W"
                    )

                    total_modules = self.modules_per_string * self.strings_per_inverter
                    self.inverter_parameters_dict["pdc0"] = (
                        self.module_parameters["pdc0"] * total_modules
                    )
                    print(
                        f"  设置逆变器pdc0为 {self.inverter_parameters_dict['pdc0']:.2f}W"
                    )
            else:
                print(
                    "警告: 无法应用性能损失，在module_parameters中未找到pdc0或I_L_ref。"
                )
                # 使用默认值
                self.module_parameters["pdc0"] = 300.0 * (1.0 - loss_factor)
                print(
                    f"  使用默认值设置模块pdc0为 {self.module_parameters['pdc0']:.2f}W"
                )

                total_modules = self.modules_per_string * self.strings_per_inverter
                self.inverter_parameters_dict["pdc0"] = (
                    self.module_parameters["pdc0"] * total_modules
                )
                print(
                    f"  设置逆变器pdc0为 {self.inverter_parameters_dict['pdc0']:.2f}W"
                )

        # 确保gamma_pdc参数存在（PVWatts模型所需）
        if "gamma_pdc" not in self.module_parameters:
            self.module_parameters["gamma_pdc"] = -0.004  # 默认温度系数-0.4%/°C
            print(
                f"  添加缺失的gamma_pdc参数，设置为默认值 {self.module_parameters['gamma_pdc']}(1/°C)"
            )

        # 修改参数后，必须更新PVSystem和ModelChain
        try:
            self._reinitialize_pvsystem_and_modelchain()
            print("  PVSystem和ModelChain已使用新参数成功重新初始化")
        except Exception as e:
            print(f"错误: 重新初始化PVSystem和ModelChain失败: {e}")
            raise

        # 更新module_states以反映此更改
        self.module_states["current_parameters"] = self.module_parameters.copy()
        if "applied_losses" not in self.module_states:
            self.module_states["applied_losses"] = []
        self.module_states["applied_losses"].append(
            {
                "loss_factor": loss_factor,
                "details": effect_details,
                "timestamp_applied": pd.Timestamp.now(),  # 或者如果可用，使用仿真时间戳
            }
        )

        print(f"性能损失 {loss_factor * 100:.2f}% 已成功应用")
        return True  # 表示损失成功应用

    def get_current_state_for_anomaly_detection(self, weather_for_step=None):
        """
        Provides a dictionary of the current PV system state relevant for anomaly detection.
        This might include latest simulated power, temperatures, and relevant weather conditions.
        Args:
            weather_for_step (dict or pd.Series, optional): Current weather data, if available.

        Returns:
            dict: Current state data.
        """
        state = {}
        if hasattr(self, "current_step_results") and self.current_step_results:
            state.update(
                self.current_step_results
            )  # Includes power, temps, effective_irradiance etc.
        else:  # Fallback if simulation step hasn't run or produced results
            state = {
                "dc_power": 0,
                "ac_power": 0,
                "temp_cell": 25,
                "p_mp": 0,
                "v_mp": 0,
                "i_mp": 0,
                "i_sc": 0,
                "v_oc": 0,
                "effective_irradiance": 0,
            }
            if weather_for_step is not None:
                state["datetime"] = (
                    weather_for_step.name
                    if isinstance(weather_for_step, pd.Series)
                    else weather_for_step.get("datetime")
                )
                state["ghi"] = weather_for_step.get("ghi", 0)
                state["temp_air"] = weather_for_step.get("temp_air", 25)
                state["wind_speed"] = weather_for_step.get("wind_speed", 1)
                state["temp_cell"] = state["temp_air"]  # Simple fallback for temp_cell
            else:
                state["datetime"] = pd.Timestamp.now(tz=self.location.tz)
                state["ghi"] = 0
                state["temp_air"] = 25
                state["wind_speed"] = 1
                state["temp_cell"] = 25
        return state

    def run_single_simulation_step(self, timestamp, weather_for_step):
        """
        Runs a single simulation step for the given timestamp and weather data.
        此方法是 SimulationEngine 与 PVDigitalTwin 的主要接口。

        Args:
            timestamp: 当前仿真时间戳（可以是任何参考，例如小时索引）。
                       底层 pvlib ModelChain 将使用 weather_for_step 中的 datetime 索引。
            weather_for_step (pd.DataFrame 或 dict): 当前步骤的天气数据。
                                                必须包含 'ghi', 'temp_air', 'wind_speed'。
                                                如果是 DataFrame，需要以 pd.DatetimeIndex 为索引。

        Returns:
            dict: 包含此步骤关键仿真结果的字典，
                  例如 {'dc_power', 'ac_power', 'temp_cell', 'p_mp', 'v_mp', 'i_mp', ...}。
        """
        # 导入numpy - 放在函数开头
        import numpy as np

        # 将weather_for_step转换为DataFrame格式，确保有正确的DatetimeIndex
        if not isinstance(weather_for_step, pd.DataFrame):
            if isinstance(weather_for_step, dict):
                if "datetime" not in weather_for_step:
                    raise ValueError(
                        "weather_for_step (dict) must contain a 'datetime' key for ModelChain."
                    )
                weather_df = pd.DataFrame([weather_for_step])
                weather_df["datetime"] = pd.to_datetime(weather_for_step["datetime"])
                weather_df = weather_df.set_index("datetime")
            elif isinstance(weather_for_step, pd.Series):
                if not isinstance(weather_for_step.name, pd.Timestamp):
                    try:
                        dt_index = pd.to_datetime([weather_for_step.name])
                    except:
                        raise ValueError(
                            "weather_for_step (Series) name must be a pd.Timestamp or convertible to it."
                        )
                else:
                    dt_index = pd.DatetimeIndex([weather_for_step.name])
                weather_df = pd.DataFrame([weather_for_step], index=dt_index)
            else:
                raise TypeError(
                    "weather_for_step must be a pandas DataFrame, Series, or dict."
                )
        else:  # 已经是DataFrame
            weather_df = weather_for_step
            if not isinstance(weather_df.index, pd.DatetimeIndex):
                if "datetime" in weather_df.columns:
                    weather_df = weather_df.set_index(
                        pd.to_datetime(weather_df["datetime"])
                    )
                else:
                    raise ValueError(
                        "weather_for_step (DataFrame) must have a DatetimeIndex or a 'datetime' column."
                    )

        # 确保weather_df只有一行
        if len(weather_df) > 1:
            weather_df = weather_df.iloc[[0]]

        # 确保有必要的列，并将它们转换为正确的类型
        required_cols = {
            "ghi": 0.0,
            "dni": None,  # 将由ghi估算
            "dhi": None,  # 将由ghi估算
            "temp_air": 25.0,
            "wind_speed": 1.0,
        }

        for col, default_val in required_cols.items():
            if col not in weather_df.columns and default_val is not None:
                weather_df[col] = default_val

        # 确保所有值都是float类型
        for col in weather_df.columns:
            if col != "datetime" and col in weather_df:
                try:
                    weather_df[col] = pd.to_numeric(weather_df[col], errors="coerce")
                    # 用0替换NaN值
                    weather_df[col] = weather_df[col].fillna(0)
                except:
                    pass

        # 如果缺少DNI和DHI，使用Erbs模型从GHI估算
        if (
            "dni" not in weather_df.columns or "dhi" not in weather_df.columns
        ) and "ghi" in weather_df.columns:
            try:
                # 获取太阳位置
                solar_position = self.location.get_solarposition(times=weather_df.index)

                # 确保天顶角正确格式化 - 根据pvlib文档，天顶角必须是度，不是弧度
                zenith = solar_position["zenith"].values.astype(float)

                # 获取一年中的日期
                doy = weather_df.index.dayofyear.values.astype(int)

                # GHI必须是浮点数
                ghi = weather_df["ghi"].values.astype(float)

                # 使用Erbs模型估算DNI和DHI
                erbs_data = pvlib.irradiance.erbs(ghi, zenith, doy)
                weather_df["dni"] = erbs_data["dni"]
                weather_df["dhi"] = erbs_data["dhi"]
            except Exception as e:
                print(f"估算DNI/DHI时出错: {e}")
                weather_df["dni"] = weather_df["ghi"] * 0.8
                weather_df["dhi"] = weather_df["ghi"] * 0.2

        # 为了避免radians方法错误，确保系统参数正确设置
        # 确保surface_tilt和surface_azimuth是标量而非数组，因为我们只有一个位置
        self.system.surface_tilt = float(30.0)  # 标量，不是数组
        self.system.surface_azimuth = float(180.0)  # 标量，不是数组

        # 尝试运行ModelChain
        try:
            # 使用最简单的方式运行ModelChain，避免复杂的错误处理
            self.model.run_model(weather=weather_df)
            success = True
        except Exception as e:
            print(
                f"Error running ModelChain at timestamp {timestamp} ({weather_df.index[0]}): {e}"
            )
            success = False

            # 如果第一次尝试失败，完全重新创建ModelChain再试一次
            try:
                # 创建一个有基本数据的简化天气DataFrame
                simple_weather = pd.DataFrame(index=weather_df.index)
                for col in ["ghi", "dni", "dhi", "temp_air", "wind_speed"]:
                    if col in weather_df.columns:
                        simple_weather[col] = weather_df[col].astype(float)
                    else:
                        simple_weather[col] = 0.0

                # 使用官方推荐的 with_pvwatts 方法重新创建ModelChain
                try:
                    # 方法1: 使用 with_pvwatts 类方法（推荐）
                    new_model = ModelChain.with_pvwatts(
                        self.system,
                        self.location,
                        name=self.location.name + "_runtime_recreate",
                    )
                    print("第二次尝试：使用 with_pvwatts 方法成功创建ModelChain")
                except Exception as e2:
                    print(f"with_pvwatts 方法也失败: {e2}，尝试显式指定参数")
                    try:
                        # 方法2: 显式指定 dc_model（备用方案）
                        new_model = ModelChain(
                            self.system,
                            self.location,
                            dc_model="pvwatts",
                            ac_model="pvwatts",
                            losses_model="pvwatts",
                            name=self.location.name + "_runtime_explicit",
                        )
                        print("第二次尝试：使用显式指定方法成功创建ModelChain")
                    except Exception as e3:
                        print(f"显式指定方法也失败: {e3}，使用最简配置")
                        new_model = ModelChain(
                            self.system,
                            self.location,
                            dc_model="pvwatts",
                            ac_model="pvwatts",
                            name=self.location.name + "_runtime_minimal",
                        )
                        print("第二次尝试：使用最简配置成功创建ModelChain")

                # 使用新模型运行
                new_model.run_model(weather=simple_weather)
                # 如果成功，用新模型替换旧模型
                self.model = new_model
                print(
                    f"ModelChain在第二次尝试中成功运行，使用了新创建的ModelChain实例。"
                )
                success = True
            except Exception as second_e:
                print(f"第二次尝试运行ModelChain仍然失败: {second_e}")
                success = False

        # 如果两次尝试都失败，使用简化计算
        if not success:
            try:
                print(f"使用简化计算方法代替ModelChain @ {timestamp}")
                # 使用简化的光伏计算
                ghi = weather_df["ghi"].iloc[0] if "ghi" in weather_df.columns else 0
                temp_air = (
                    weather_df["temp_air"].iloc[0]
                    if "temp_air" in weather_df.columns
                    else 25
                )

                # 简化的功率计算
                if ghi > 0:
                    # 简单的线性关系：功率与辐照度成正比
                    dc_power = (ghi / 1000.0) * self.module_parameters.get(
                        "pdc0", 1000
                    )  # 标准测试条件下的功率
                    # 温度修正
                    temp_coeff = self.module_parameters.get("gamma_pdc", -0.004)
                    temp_correction = 1 + temp_coeff * (temp_air - 25)
                    dc_power *= temp_correction

                    # 简化的逆变器效率
                    inverter_efficiency = 0.95
                    ac_power = dc_power * inverter_efficiency
                else:
                    dc_power = 0
                    ac_power = 0

                # 创建简化的结果
                self.model.dc = pd.DataFrame(
                    {
                        "p_mp": [dc_power],
                        "v_mp": [30.0],  # 假设电压
                        "i_mp": [dc_power / 30.0 if dc_power > 0 else 0],
                        "i_sc": [dc_power / 25.0 if dc_power > 0 else 0],
                        "v_oc": [36.0],  # 假设开路电压
                    },
                    index=weather_df.index,
                )

                self.model.ac = pd.Series([ac_power], index=weather_df.index)
                self.model.cell_temperature = pd.Series(
                    [temp_air + 3], index=weather_df.index
                )  # 组件温度略高于环境温度

                success = True
                print(
                    f"简化计算成功 @ {timestamp}: DC={dc_power:.1f}W, AC={ac_power:.1f}W"
                )
            except Exception as e3:
                print(f"简化计算也失败: {e3}")
                # 返回默认值
                return {
                    "datetime": weather_df.index[0],
                    "dc_power": 0,
                    "ac_power": 0,
                    "temp_cell": (
                        weather_df["temp_air"].iloc[0]
                        if "temp_air" in weather_df.columns
                        else 25
                    ),
                    "p_mp": 0,
                    "v_mp": 0,
                    "i_mp": 0,
                    "i_sc": 0,
                    "v_oc": 0,
                    "effective_irradiance": 0,
                    "ghi": (
                        weather_df["ghi"].iloc[0] if "ghi" in weather_df.columns else 0
                    ),
                    "temp_air": (
                        weather_df["temp_air"].iloc[0]
                        if "temp_air" in weather_df.columns
                        else 25
                    ),
                    "wind_speed": (
                        weather_df["wind_speed"].iloc[0]
                        if "wind_speed" in weather_df.columns
                        else 1
                    ),
                    "error": "所有计算方法都失败",
                }

        # 从ModelChain提取结果
        try:
            # 提取直流功率结果
            if hasattr(self.model, "dc") and self.model.dc is not None:
                if (
                    isinstance(self.model.dc, pd.DataFrame)
                    and "p_mp" in self.model.dc.columns
                ):
                    p_dc_step = self.model.dc["p_mp"].iloc[0]
                    v_dc_step = (
                        self.model.dc["v_mp"].iloc[0]
                        if "v_mp" in self.model.dc.columns
                        else 0
                    )
                    i_dc_step = (
                        self.model.dc["i_mp"].iloc[0]
                        if "i_mp" in self.model.dc.columns
                        else 0
                    )
                    i_sc_step = (
                        self.model.dc["i_sc"].iloc[0]
                        if "i_sc" in self.model.dc.columns
                        else 0
                    )
                    v_oc_step = (
                        self.model.dc["v_oc"].iloc[0]
                        if "v_oc" in self.model.dc.columns
                        else 0
                    )
                else:
                    # 如果dc不是DataFrame或没有p_mp列
                    p_dc_step = (
                        self.model.dc.iloc[0]
                        if isinstance(self.model.dc, pd.Series)
                        else 0
                    )
                    v_dc_step = 0
                    i_dc_step = 0
                    i_sc_step = 0
                    v_oc_step = 0
            else:
                p_dc_step = 0
                v_dc_step = 0
                i_dc_step = 0
                i_sc_step = 0
                v_oc_step = 0

            # 有效辐照度
            effective_irradiance_step = 0
            if (
                hasattr(self.model, "effective_irradiance")
                and self.model.effective_irradiance is not None
            ):
                if not self.model.effective_irradiance.empty:
                    effective_irradiance_step = self.model.effective_irradiance.iloc[0]

            # 电池温度
            temp_cell_step = (
                weather_df["temp_air"].iloc[0]
                if "temp_air" in weather_df.columns
                else 25
            )  # 默认值
            if (
                hasattr(self.model, "cell_temperature")
                and self.model.cell_temperature is not None
            ):
                if not self.model.cell_temperature.empty:
                    temp_cell_step = self.model.cell_temperature.iloc[0]

            # 交流功率
            p_ac_step = 0
            if hasattr(self.model, "ac") and self.model.ac is not None:
                if not isinstance(self.model.ac, type(None)) and not (
                    hasattr(self.model.ac, "empty") and self.model.ac.empty
                ):
                    p_ac_step = (
                        self.model.ac.iloc[0]
                        if isinstance(self.model.ac, (pd.Series, pd.DataFrame))
                        else self.model.ac
                    )

            # 处理NaN值
            p_dc_step = 0 if pd.isna(p_dc_step) else p_dc_step
            p_ac_step = 0 if pd.isna(p_ac_step) else p_ac_step

            # 变压器损耗
            if p_ac_step > 0:
                transformer_loss = p_ac_step * (1 - self.transformer_efficiency)
                p_ac_final = p_ac_step - transformer_loss
                p_ac_final = min(p_ac_final, self.transformer_capacity_w)
            else:
                p_ac_final = 0

            # 存储结果
            self.current_step_results = {
                "datetime": weather_df.index[0],
                "dc_power": p_dc_step / 1000,  # 转换为kW
                "ac_power": p_ac_final / 1000,  # 转换为kW
                "temp_cell": temp_cell_step,
                "p_mp": p_dc_step / 1000,  # kW
                "v_mp": v_dc_step,
                "i_mp": i_dc_step,
                "i_sc": i_sc_step,
                "v_oc": v_oc_step,
                "effective_irradiance": effective_irradiance_step,
                "ghi": weather_df["ghi"].iloc[0] if "ghi" in weather_df.columns else 0,
                "temp_air": (
                    weather_df["temp_air"].iloc[0]
                    if "temp_air" in weather_df.columns
                    else 25
                ),
                "wind_speed": (
                    weather_df["wind_speed"].iloc[0]
                    if "wind_speed" in weather_df.columns
                    else 1
                ),
            }
            return self.current_step_results

        except Exception as result_e:
            print(f"提取ModelChain结果时出错: {result_e}")
            # 返回基本的默认结果
            return {
                "datetime": weather_df.index[0],
                "dc_power": 0,
                "ac_power": 0,
                "temp_cell": (
                    weather_df["temp_air"].iloc[0]
                    if "temp_air" in weather_df.columns
                    else 25
                ),
                "p_mp": 0,
                "v_mp": 0,
                "i_mp": 0,
                "i_sc": 0,
                "v_oc": 0,
                "effective_irradiance": 0,
                "ghi": weather_df["ghi"].iloc[0] if "ghi" in weather_df.columns else 0,
                "temp_air": (
                    weather_df["temp_air"].iloc[0]
                    if "temp_air" in weather_df.columns
                    else 25
                ),
                "wind_speed": (
                    weather_df["wind_speed"].iloc[0]
                    if "wind_speed" in weather_df.columns
                    else 1
                ),
                "error": f"提取结果错误: {str(result_e)}",
            }

    def calculate_energy_yield(self, power_values, freq="1h"):
        """
        计算给定功率值的能量产量。

        Args:
            power_values (np.array or list): 功率值（kW）
            freq (str): 功率值的采样频率，默认为'1h'（每小时）

        Returns:
            float: 能量产量（kWh）
        """
        import numpy as np

        if not isinstance(power_values, (list, np.ndarray)) and hasattr(
            power_values, "values"
        ):
            # 如果是Series或DataFrame列，转换为numpy数组
            power_values = power_values.values

        # 将功率值转换为numpy数组
        power_array = np.array(power_values)

        # 删除任何NaN值
        power_array = power_array[~np.isnan(power_array)]

        if len(power_array) == 0:
            return 0.0

        # 从频率字符串中提取时间步长（小时）
        if freq == "1h" or freq == "H" or freq == "h":
            time_step_hours = 1.0
        elif freq == "30min" or freq == "30T":
            time_step_hours = 0.5
        elif freq == "15min" or freq == "15T":
            time_step_hours = 0.25
        elif freq == "10min" or freq == "10T":
            time_step_hours = 1 / 6
        elif freq == "5min" or freq == "5T":
            time_step_hours = 1 / 12
        elif freq == "1min" or freq == "1T" or freq == "T":
            time_step_hours = 1 / 60
        else:
            # 尝试从字符串中解析时间步长
            import re

            match = re.match(r"(\d+)([a-zA-Z]+)", freq)
            if match:
                value, unit = match.groups()
                if unit.lower() in ["h", "hour", "hours"]:
                    time_step_hours = float(value)
                elif unit.lower() in ["m", "min", "minute", "minutes", "t"]:
                    time_step_hours = float(value) / 60
                elif unit.lower() in ["s", "sec", "second", "seconds"]:
                    time_step_hours = float(value) / 3600
                else:
                    print(f"警告: 无法识别的时间单位 '{unit}'，使用默认值1小时")
                    time_step_hours = 1.0
            else:
                print(f"警告: 无法解析频率字符串 '{freq}'，使用默认值1小时")
                time_step_hours = 1.0

        # 计算能量（功率×时间）
        energy_kwh = np.sum(power_array) * time_step_hours

        return energy_kwh


def get_solar_position(latitude, longitude, date, tz="Asia/Shanghai"):
    """
    计算指定日期和位置的太阳位置

    参数:
    -----
    latitude : float
        纬度
    longitude : float
        经度
    date : str
        日期，格式为YYYY-MM-DD
    tz : str, 可选
        时区，默认为'Asia/Shanghai'

    返回:
    -----
    pandas.DataFrame
        包含太阳位置信息的数据框（天顶角、方位角等）
    """
    # 创建位置对象
    location = Location(latitude, longitude, tz=tz)

    # 创建时间索引
    times = pd.date_range(
        start=date, end=pd.Timestamp(date) + pd.Timedelta(days=1), freq="5min", tz=tz
    )[:-1]

    # 计算太阳位置
    solpos = pvlib.solarposition.get_solarposition(times, latitude, longitude)

    return solpos
