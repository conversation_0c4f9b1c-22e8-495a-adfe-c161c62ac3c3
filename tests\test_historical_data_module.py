#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
历史数据模块测试

本测试文件测试历史数据模块的主要功能，包括：
1. 数据加载
2. 数据预处理
3. 特征提取
4. 数据标准化
5. 数据分割
"""

import os
import sys
import unittest
import tempfile
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

# 导入历史数据模块
from src.data_layer.historical_data_module import HistoricalDataModule
from src.utils.data_utils import generate_synthetic_weather_data


class TestHistoricalDataModule(unittest.TestCase):
    """历史数据模块测试类"""

    @classmethod
    def setUpClass(cls):
        """设置测试环境"""
        # 创建临时目录
        cls.temp_dir = tempfile.TemporaryDirectory()
        cls.data_dir = os.path.join(cls.temp_dir.name, "data")
        os.makedirs(cls.data_dir, exist_ok=True)

        # 生成测试数据
        cls.generate_test_data()

        # 创建历史数据模块实例
        cls.hdm = HistoricalDataModule()

    @classmethod
    def tearDownClass(cls):
        """清理测试环境"""
        cls.temp_dir.cleanup()

    @classmethod
    def generate_test_data(cls):
        """生成测试数据"""
        # 生成天气数据
        start_date = (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d")
        end_date = datetime.now().strftime("%Y-%m-%d")

        weather_data = generate_synthetic_weather_data(
            start_date=start_date,
            end_date=end_date,
            latitude=39.9,
            longitude=116.4,
            freq="1h",
        )

        # 生成光伏系统数据
        pv_system_data = pd.DataFrame(index=weather_data.index)
        pv_system_data["datetime"] = weather_data["datetime"]

        # 模拟直流功率
        efficiency = 0.15
        area = 100  # 平方米
        pv_system_data["dc_power"] = (
            weather_data["ghi"] * efficiency * area / 1000
        )  # kW

        # 模拟交流功率
        inverter_efficiency = 0.95
        pv_system_data["ac_power"] = pv_system_data["dc_power"] * inverter_efficiency

        # 添加温度效应
        temp_coeff = -0.004
        pv_system_data["dc_power"] *= 1 + temp_coeff * (weather_data["temp_air"] - 25)
        pv_system_data["ac_power"] *= 1 + temp_coeff * (weather_data["temp_air"] - 25)

        # 确保功率不为负
        pv_system_data["dc_power"] = pv_system_data["dc_power"].clip(0)
        pv_system_data["ac_power"] = pv_system_data["ac_power"].clip(0)

        # 添加一些缺失值和异常值
        missing_indices = np.random.choice(
            pv_system_data.index, size=int(len(pv_system_data) * 0.05), replace=False
        )
        pv_system_data.loc[missing_indices, "ac_power"] = np.nan

        anomaly_indices = np.random.choice(
            pv_system_data.index, size=int(len(pv_system_data) * 0.02), replace=False
        )
        pv_system_data.loc[anomaly_indices, "ac_power"] *= 2.5

        # 保存数据
        cls.weather_file = os.path.join(cls.data_dir, "weather_data.csv")
        cls.pv_file = os.path.join(cls.data_dir, "pv_system_data.csv")

        weather_data.to_csv(cls.weather_file, index=False)
        pv_system_data.to_csv(cls.pv_file, index=False)

        # 存储数据引用
        cls.weather_data = weather_data
        cls.pv_system_data = pv_system_data

    def test_load_dataset(self):
        """测试数据加载功能"""
        # 加载天气数据
        weather_df = self.hdm.load_dataset(
            name="weather",
            file_path=self.weather_file,
            time_col="datetime",
            preprocess=False,
        )

        # 验证数据加载正确
        self.assertEqual(len(weather_df), len(self.weather_data))
        self.assertTrue("ghi" in weather_df.columns)
        self.assertTrue("temp_air" in weather_df.columns)

        # 加载光伏系统数据
        pv_df = self.hdm.load_dataset(
            name="pv_system",
            file_path=self.pv_file,
            time_col="datetime",
            preprocess=False,
        )

        # 验证数据加载正确
        self.assertEqual(len(pv_df), len(self.pv_system_data))
        self.assertTrue("dc_power" in pv_df.columns)
        self.assertTrue("ac_power" in pv_df.columns)

    def test_preprocess_dataset(self):
        """测试数据预处理功能"""
        # 加载数据
        pv_df = self.hdm.load_dataset(
            name="pv_raw", file_path=self.pv_file, time_col="datetime", preprocess=False
        )

        # 检查原始数据中是否有缺失值
        self.assertTrue(pv_df["ac_power"].isna().any())

        # 进行预处理
        processed_df = self.hdm.preprocess_dataset(
            df=pv_df,
            time_col="datetime",
            handle_missing="interpolate",
            detect_outliers=True,
        )

        # 验证预处理结果
        # 1. 缺失值应该被处理
        self.assertFalse(processed_df["ac_power"].isna().any())

        # 2. 异常值应该被检测和处理
        # 注意：这里我们不能直接比较异常值的数量，因为预处理可能会将一些值标记为异常
        # 但我们可以检查是否有明显的异常值被处理
        max_power = processed_df["ac_power"].max()
        self.assertLess(max_power, pv_df["ac_power"].max() * 0.9)  # 异常值应该被缩小

    def test_extract_features(self):
        """测试特征提取功能"""
        # 加载并预处理数据
        pv_df = self.hdm.load_dataset(
            name="pv_for_features",
            file_path=self.pv_file,
            time_col="datetime",
            preprocess=True,
        )

        # 提取特征
        features_df = self.hdm.extract_features_from_dataset(
            df=pv_df,
            dataset_name="pv_features",
            window_size=6,
            features=["mean", "std", "min", "max"],
        )

        # 验证特征提取结果
        # 1. 特征数量应该是原始数值列数量的4倍（mean, std, min, max）
        num_numeric_cols = len(pv_df.select_dtypes(include=[np.number]).columns)
        expected_feature_cols = num_numeric_cols * 4
        self.assertEqual(len(features_df.columns), expected_feature_cols)

        # 2. 检查是否包含预期的特征列
        self.assertTrue("ac_power_mean" in features_df.columns)
        self.assertTrue("ac_power_std" in features_df.columns)
        self.assertTrue("ac_power_min" in features_df.columns)
        self.assertTrue("ac_power_max" in features_df.columns)

    def test_standardize_dataset(self):
        """测试数据标准化功能"""
        # 加载并预处理数据
        pv_df = self.hdm.load_dataset(
            name="pv_for_standardize",
            file_path=self.pv_file,
            time_col="datetime",
            preprocess=True,
        )

        # 标准化数据
        standardized_df = self.hdm.standardize_dataset(
            df=pv_df,
            dataset_name="pv_standardized",
            method="minmax",
            feature_range=(0, 1),
        )

        # 验证标准化结果
        # 1. 数据应该在0-1范围内
        for col in standardized_df.select_dtypes(include=[np.number]).columns:
            self.assertGreaterEqual(standardized_df[col].min(), 0)
            self.assertLessEqual(standardized_df[col].max(), 1)

        # 2. 检查缩放器是否正确保存
        self.assertTrue("pv_standardized" in self.hdm.scalers)
        self.assertEqual(self.hdm.scalers["pv_standardized"]["method"], "minmax")

    def test_split_dataset(self):
        """测试数据分割功能"""
        # 加载并预处理数据
        pv_df = self.hdm.load_dataset(
            name="pv_for_split",
            file_path=self.pv_file,
            time_col="datetime",
            preprocess=True,
        )

        # 分割数据
        target_col = "ac_power"
        split_result = self.hdm.split_dataset(
            df=pv_df,
            dataset_name="pv_split",
            target_col=target_col,
            test_size=0.2,
            val_size=0.1,
        )

        # 验证分割结果
        # 1. 检查是否包含预期的键
        self.assertTrue("data" in split_result)
        self.assertTrue("info" in split_result)

        # 2. 检查数据集是否正确分割
        data = split_result["data"]
        self.assertTrue("X_train" in data)
        self.assertTrue("y_train" in data)
        self.assertTrue("X_val" in data)
        self.assertTrue("y_val" in data)
        self.assertTrue("X_test" in data)
        self.assertTrue("y_test" in data)

        # 3. 检查分割比例是否正确
        total_samples = len(pv_df)
        test_samples = len(data["X_test"])
        val_samples = len(data["X_val"])
        train_samples = len(data["X_train"])

        self.assertAlmostEqual(test_samples / total_samples, 0.2, delta=0.05)
        self.assertAlmostEqual(val_samples / total_samples, 0.1, delta=0.05)
        self.assertAlmostEqual(train_samples / total_samples, 0.7, delta=0.05)

    def test_save_and_load(self):
        """测试数据保存和加载功能"""
        # 加载数据
        pv_df = self.hdm.load_dataset(
            name="pv_for_save",
            file_path=self.pv_file,
            time_col="datetime",
            preprocess=True,
        )

        # 保存数据
        save_path = os.path.join(self.data_dir, "saved_data.csv")
        self.hdm.save_dataset(name="pv_for_save", file_path=save_path, format="csv")

        # 验证文件是否创建
        self.assertTrue(os.path.exists(save_path))

        # 保存元数据
        metadata_path = os.path.join(self.data_dir, "metadata.json")
        self.hdm.save_metadata(file_path=metadata_path)

        # 验证元数据文件是否创建
        self.assertTrue(os.path.exists(metadata_path))


if __name__ == "__main__":
    unittest.main()
